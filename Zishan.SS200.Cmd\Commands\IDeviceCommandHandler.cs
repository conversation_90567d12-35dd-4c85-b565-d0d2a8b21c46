using System;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Services;

namespace Zishan.SS200.Cmd.Commands
{
    /// <summary>
    /// 设备命令处理器接口
    /// </summary>
    /// <typeparam name="TDevice">设备类型</typeparam>
    /// <typeparam name="TCommand">命令枚举类型</typeparam>
    public interface IDeviceCommandHandler<TDevice, TCommand> where TDevice : Services.McuDevice where TCommand : Enum
    {
        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="device">设备实例</param>
        /// <param name="command">命令</param>
        /// <param name="parameters">命令参数</param>
        /// <returns>执行结果</returns>
        Task<Services.TaskHandleResult> ExecuteCommandAsync(TDevice device, TCommand command, ushort[] parameters = null);

        /// <summary>
        /// 验证命令参数
        /// </summary>
        /// <param name="command">命令</param>
        /// <param name="parameters">命令参数</param>
        /// <returns>验证结果</returns>
        bool ValidateParameters(TCommand command, ushort[] parameters);
    }
} 