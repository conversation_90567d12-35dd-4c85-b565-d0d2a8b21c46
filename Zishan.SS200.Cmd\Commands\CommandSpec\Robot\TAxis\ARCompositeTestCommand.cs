using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using log4net;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Utilities;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;
using Zishan.SS200.Cmd.Enums.Command;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Commands.CommandSpec.Robot.TAxis
{
    /// <summary>
    /// ARCompositeTest - 复合命令测试（调用AR1+其他命令=2+1=3）
    /// </summary>
    public class ARCompositeTestCommand : BaseCommandSpec<EnuRobotCmd>
    {
        /// <summary>
        /// 状态管理实例
        /// </summary>
        private readonly S200MockStatus _mockStatus = S200MockStatus.Instance;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ARCompositeTestCommand() : base(
            EnuMcuDeviceType.Robot,
            EnuRobotCmd.ARCompositeTest,
            EnuRobotCmd.ARCompositeTest.ToString(),
            "Composite Command Test",
            "复合命令测试",
            45000) // 由于是复合命令，超时时间设置更长
        {
        }

        /// <summary>
        /// 参数验证
        /// </summary>
        /// <param name="parameters">参数列表</param>
        /// <returns>是否有效</returns>
        public override bool ValidateParameters(List<ushort> parameters)
        {
            // 当前命令不需要参数
            return true;
        }

        /// <summary>
        /// 执行前处理 - 复合命令的前置条件检查
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>是否继续执行命令</returns>
        public override async Task<bool> BeforeExecuteAsync(IS200McuCmdService cmdService, List<ushort> parameters)
        {
            _logger.Info("开始执行复合命令测试前置检查");

            // 1. 检查机器人状态
            if (!_mockStatus.CheckRobotStatus(cmdService))
            {
                return false;
            }

            // 2. 检查T轴位置状态 - 需要确定当前是否已在RP2位置
            bool isAtRP2 = await _mockStatus.IsRobotAtPositionAsync(cmdService, "RP2");
            if (isAtRP2)
            {
                _logger.Info("T轴当前已在RP2位置(工艺腔室B)，命令完成");
                return false; // 已经在目标位置，不需要执行命令
            }

            // 3. 检查R轴位置状态
            bool isAtRS18 = await _mockStatus.IsRobotAtPositionAsync(cmdService, "RS18");
            if (!isAtRS18)
            {
                _logger.Error("R轴不在零位(RS18)，无法执行T轴移动");
                return false;
            }

            // 通过所有前置检查，可以执行命令
            _logger.Info("复合命令测试前置检查通过，准备执行复合命令");
            return await base.BeforeExecuteAsync(cmdService, parameters);
        }

        /// <summary>
        /// 执行后处理 - 复合命令实现，调用AR1Command和其他操作
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="result">命令执行结果</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>处理后的结果</returns>
        public override async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> AfterExecuteAsync(
            IS200McuCmdService cmdService,
            (string Response, ushort RunInfo, ushort ReturnInfo) result,
            List<ushort> parameters)
        {
            if (result.ReturnInfo == 0)
            {
                _logger.Info("开始执行复合命令测试流程");

                // 步骤1: 首先调用AR1Command，使T轴移动到工艺腔室A
                _logger.Info("调用AR1命令，T轴Smooth端移动到工艺腔室A");
                // 这里需要检查EnuRobotCmdIndex中是否有AR1枚举值，如果没有，需要使用ExecuteDeviceCommandAsync方法
                var ar1Command = new AR1Command();
                var ar1Result = await cmdService.ExecuteDeviceCommandAsync(EnuMcuDeviceType.Robot, EnuRobotCmdIndex.AR1, parameters?.ToArray());

                if (ar1Result == Services.TaskHandleResult.Failed)
                {
                    _logger.Error($"执行AR1命令失败");
                    return (result.Response + " - AR1命令执行失败", result.RunInfo, 0x0201);
                }

                _logger.Info("AR1命令执行成功，T轴已移动到工艺腔室A");

                // 步骤2: 从工艺腔室A移动到工艺腔室B
                _logger.Info("开始从工艺腔室A移动到工艺腔室B");

                // 2.1 检查是否已经在工艺腔室A位置
                bool isAtRP1 = await _mockStatus.IsRobotAtPositionAsync(cmdService, "RP1");
                if (!isAtRP1)
                {
                    _logger.Error("T轴未到达工艺腔室A位置，无法继续执行");
                    return (result.Response + " - T轴位置错误", result.RunInfo, 0x0202);
                }

                // 2.2 使用AR10命令，直接移动T轴到RP2位置(工艺腔室B)
                _logger.Info("执行AR10命令，T轴移动到RP2位置(工艺腔室B)");
                // 调用执行T轴移动的命令，传递RP2位置对应的步进值和其他参数
                var ar10Result = await cmdService.ExecuteRobotTAxisCommandAsync(
                    50000,  // RP2位置对应的步进值
                    12000,  // 起始斜率
                    12000,  // 终止斜率
                    600     // 运行电流
                );

                if (ar10Result.ReturnInfo != 0)
                {
                    _logger.Error($"T轴移动到工艺腔室B(RP2)位置失败: {ar10Result.Response}");
                    return (result.Response + " - T轴移动到工艺腔室B位置失败", result.RunInfo, 0x0203);
                }

                // 2.3 验证是否成功移动到RP2位置
                bool movedToRP2 = await _mockStatus.IsRobotAtPositionAsync(cmdService, "RP3");
                if (!movedToRP2)
                {
                    _logger.Error("T轴未能成功移动到工艺腔室B位置");
                    return (result.Response + " - 移动验证失败", result.RunInfo, 0x0204);
                }

                _logger.Info("复合命令测试流程执行完成，T轴成功移动到工艺腔室B");
                return (result.Response + " - 复合命令流程执行完成", result.RunInfo, 0);
            }
            else
            {
                _logger.Error($"复合命令测试执行失败，错误代码: 0x{result.ReturnInfo:X4}");

                // 根据错误代码提供更详细的错误信息
                string errorDetail = _mockStatus.GetRobotErrorDetail(result.ReturnInfo);
                if (!string.IsNullOrEmpty(errorDetail))
                {
                    result.Response = $"{result.Response} - {errorDetail}";
                }
            }

            return await base.AfterExecuteAsync(cmdService, result, parameters);
        }
    }
}