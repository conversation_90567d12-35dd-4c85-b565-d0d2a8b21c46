using log4net;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Net;
using System.Threading.Tasks;
using System.Timers;
using Zishan.SS200.Cmd.Commands;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Models;
using NModbus;
using Newtonsoft.Json;
using System.IO;
using System.Linq;
using Zishan.SS200.Cmd.Services.Interfaces;
using System.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Config;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;
using Zishan.SS200.Cmd.Enums.SS200.IOInterface.Robot;
using Zishan.SS200.Cmd.Utilities;
using JsonNamingPolicy = System.Text.Json.JsonNamingPolicy;
using JsonSerializerOptions = System.Text.Json.JsonSerializerOptions;
using JsonPropertyName = System.Text.Json.Serialization.JsonPropertyNameAttribute;

namespace Zishan.SS200.Cmd.Services
{
    /// <summary>
    /// Modbus客户端工厂
    /// </summary>
    public class ModbusClientFactory
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ModbusClientFactory));
        private readonly Dictionary<EnuMcuDeviceType, IModbusClientService> _clients = new Dictionary<EnuMcuDeviceType, IModbusClientService>();

        /// <summary>
        /// 获取或创建Modbus客户端
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>Modbus客户端服务</returns>
        public IModbusClientService GetOrCreateClient(EnuMcuDeviceType deviceType)
        {
            if (!_clients.ContainsKey(deviceType))
            {
                _clients[deviceType] = new ModbusClientService();
                _logger.Info($"为设备 {deviceType} 创建新的Modbus客户端实例");
            }
            return _clients[deviceType];
        }
    }

    /// <summary>
    /// S200 MCU命令服务
    /// 支持Shuttle、Robot、ChamberA、Chb四个设备的独立Modbus TCP连接
    /// </summary>
    public partial class S200McuCmdService : ObservableObject, IS200McuCmdService
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(S200McuCmdService));
        private readonly ModbusClientFactory _clientFactory;
        private static readonly Lazy<S200McuCmdService> _instance = new Lazy<S200McuCmdService>(() => new S200McuCmdService());
        private IModbusMaster _modbusMaster;
        private bool _isConnected;

        // 添加DeviceCommandFactory成员
        private readonly DeviceCommandFactory _commandFactory;

        // 线圈监控相关成员
        private System.Timers.Timer _coilMonitorTimer;

        private System.Timers.Timer _alarmMonitorTimer;

        private bool _isMonitoring;
        private bool _isAlarmMonitoring;
        private const int CoilMonitorInterval = 100; // 监控间隔100ms
        private const int AlarmMonitorInterval = 100; // 优化报警监控间隔20ms，人眼可接受
        private const ushort CoilCount = 32; // 线圈数量0-31
        private const ushort AlarmRegisterStartAddress = 0x100; // 报警寄存器起始地址

        /// <summary>
        /// 实时读取的寄存器数量：报警寄存器数量+ 位置寄存器数量+ PinSearch寄存器数量
        /// </summary>
        private const ushort AlarmRegisterCount = 13;

        // 用于取消正在进行的操作
        private CancellationTokenSource _operationCts = new CancellationTokenSource();

        // 设备线圈读取功能支持状态
        private readonly Dictionary<EnuMcuDeviceType, bool> _deviceCoilSupportStatus = new Dictionary<EnuMcuDeviceType, bool>();

        // 设备线圈集合，用于只读传感器状态展示：？ False True
        public ObservableCollection<ModbusCoil> ShuttleInputCoils { get; private set; }
        public ObservableCollection<ModbusCoil> RobotInputCoils { get; private set; }
        public ObservableCollection<ModbusCoil> ChaInputCoils { get; private set; }
        public ObservableCollection<ModbusCoil> ChbInputCoils { get; private set; }

        // 设备线圈集合，用于读写状态展示，用于控制，比如：Shuttle的下降、上升、旋转等
        public ObservableCollection<ModbusCoil> ShuttleCoils { get; private set; }
        public ObservableCollection<ModbusCoil> RobotCoils { get; private set; }
        public ObservableCollection<ModbusCoil> ChaCoils { get; private set; }
        public ObservableCollection<ModbusCoil> ChbCoils { get; private set; }

        /*电机寄存器映射区
        0x0100		当前Alarm(T-Axis)
        0x0101		当前Alarm(R-Axis)
        0x0102		当前Alarm(Z-Axis)
        0x0103		检测位置H(T-Axis)
        0x0104		检测位置L(T-Axis)
        0x0105		检测位置H(R-Axis)
        0x0106		检测位置L(R-Axis)
        0x0107		检测位置H(Z-Axis)
        0x0108		检测位置L(Z-Axis)
        0x0109		PinSearch P1 H
        0x010A		PinSearch P1 L
        0x010B		PinSearch P2 H
        0x010C		PinSearch P2 L
         */
        /// <summary>
        /// Robot电机寄存器映射区 报警、RTZ位置信息，使用ModbusRegister类型
        /// </summary>
        public ObservableCollection<ModbusRegister> RobotAlarmRegisters { get; private set; } = new ObservableCollection<ModbusRegister>();

        private readonly Dictionary<int, ModbusRegister> _registers = new();

        /// <summary>
        /// 设备线圈名称配置
        /// </summary>
        private Dictionary<EnuMcuDeviceType, DeviceCoilConfig> _deviceCoilConfig;

        public static S200McuCmdService Instance => _instance.Value;

        // 四个设备实例
        public McuDevice Shuttle { get; }
        public McuDevice Robot { get; }
        public McuDevice ChamberA { get; }
        public McuDevice ChamberB { get; }

        public string Name { get; set; }

        /// </summary>
        /// Robot Nose 做Pin Search 获取到的值，已经计算中心值
        /// </summary>
        [ObservableProperty]
        private int _NoseBasePinSearchValue;

        /// <summary>
        /// Robot Smooth 做Pin Search 获取到的值，已经计算中心值
        /// </summary>
        [ObservableProperty]
        private int _SmoothBasePinSearchValue;

        /// <summary>
        /// 构造函数
        /// </summary>
        public S200McuCmdService()
        {
            _clientFactory = new ModbusClientFactory();

            // 为每个设备创建独立的Modbus客户端实例
            Shuttle = new McuDevice(EnuMcuDeviceType.Shuttle, _clientFactory.GetOrCreateClient(EnuMcuDeviceType.Shuttle), 1);
            Robot = new McuDevice(EnuMcuDeviceType.Robot, _clientFactory.GetOrCreateClient(EnuMcuDeviceType.Robot), 2);
            ChamberA = new McuDevice(EnuMcuDeviceType.ChamberA, _clientFactory.GetOrCreateClient(EnuMcuDeviceType.ChamberA), 3);
            ChamberB = new McuDevice(EnuMcuDeviceType.ChamberB, _clientFactory.GetOrCreateClient(EnuMcuDeviceType.ChamberB), 4);

            // 初始化命令工厂
            _commandFactory = new DeviceCommandFactory(_clientFactory.GetOrCreateClient(EnuMcuDeviceType.Shuttle), _logger);

            // 加载设备线圈名称配置
            LoadDeviceCoilConfig();

            // 初始化线圈集合
            InitializeCoilCollections();

            // 初始化报警寄存器
            InitializeAlarmRegisters();

            // 创建但不启动线圈监控定时器
            _coilMonitorTimer = new System.Timers.Timer(CoilMonitorInterval);
            _coilMonitorTimer.Elapsed += async (s, e) => await UpdateCoilsAsync();
            _coilMonitorTimer.AutoReset = true;

            // 创建但不启动报警监控定时器
            _alarmMonitorTimer = new System.Timers.Timer(AlarmMonitorInterval);
            _alarmMonitorTimer.Elapsed += async (s, e) => await UpdateAlarmRegistersAsync();
            _alarmMonitorTimer.AutoReset = true;

            _logger.Info("S200 MCU命令服务已初始化");
        }

        #region 方便通过设备类型、对应DI、DO枚举类型获取线圈实体对象，用于SubSystemStatus\Shuttle\ShuttleSlotStatusManager.cs和SubSystemStatus\Chamber\ChamberSubsystemStatus.cs计算状态

        /*
        通过设备类型、对应DI、DO枚举类型获取线圈实体对象
        */

        public void GetModbusCoil(EnuMcuDeviceType deviceType, out ObservableCollection<ModbusCoil> inputCoils, out ObservableCollection<ModbusCoil> controlCoils)
        {
            inputCoils = null;
            controlCoils = null;
            switch (deviceType)
            {
                case EnuMcuDeviceType.Shuttle:
                    inputCoils = ShuttleInputCoils;
                    controlCoils = ShuttleCoils;
                    break;

                case EnuMcuDeviceType.Robot:
                    inputCoils = RobotInputCoils;
                    controlCoils = RobotCoils;
                    break;

                case EnuMcuDeviceType.ChamberA:
                    inputCoils = ChaInputCoils;
                    controlCoils = ChaCoils;
                    break;

                case EnuMcuDeviceType.ChamberB:
                    inputCoils = ChbInputCoils;
                    controlCoils = ChbCoils;
                    break;

                default:
                    _logger.Warn($"未知设备类型: {deviceType}");
                    break;
            }
        }

        public void GetRobotSpecilModbusCoil(EnuRobotDICodes enuRobotDICodes, out ModbusCoil inputCoil)
        {
            ObservableCollection<ModbusCoil> inputCoils;
            ObservableCollection<ModbusCoil> controlCoils;

            GetModbusCoil(EnuMcuDeviceType.Robot, out inputCoils, out controlCoils);
            // 如果设备类型不支持线圈读取，抛出异常
            if (inputCoils == null || controlCoils == null)
            {
                throw new NotSupportedException($"设备 {EnuMcuDeviceType.Robot} 不支持线圈读取");
            }
            // 检查是否已连接设备
            // if (!IsDeviceConnected(deviceType))
            // {
            //     throw new InvalidOperationException($"设备 {deviceType} 未连接");
            // }

            var ioCode = enuRobotDICodes.ToString().Split("_", StringSplitOptions.RemoveEmptyEntries)[0];
            inputCoil = inputCoils.FirstOrDefault(c => c.IoCode.Equals(ioCode, StringComparison.OrdinalIgnoreCase));
        }

        #region 通用线圈查找方法

        /// <summary>
        /// 通过设备类型和IO代码获取ModbusCoil
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="ioCode">IO代码，如SDI1、PDI12、RDI1等</param>
        /// <param name="isInput">是否为输入线圈(DI)，false表示输出线圈(DO)</param>
        /// <returns>找到的ModbusCoil，未找到返回null</returns>
        public ModbusCoil GetModbusCoilByIOCode(EnuMcuDeviceType deviceType, string ioCode, bool isInput = true)
        {
            if (string.IsNullOrEmpty(ioCode))
                return null;

            ObservableCollection<ModbusCoil> inputCoils;
            ObservableCollection<ModbusCoil> controlCoils;

            GetModbusCoil(deviceType, out inputCoils, out controlCoils);

            // 根据isInput参数选择对应的线圈集合
            var targetCoils = isInput ? inputCoils : controlCoils;

            if (targetCoils == null)
            {
                _logger.Warn($"设备 {deviceType} 不支持{(isInput ? "输入" : "输出")}线圈读取");
                return null;
            }

            // 在目标集合中查找匹配的线圈
            return targetCoils.FirstOrDefault(c => c.IoCode.Equals(ioCode, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 通过枚举值获取输入线圈(DI)
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="deviceType">设备类型</param>
        /// <param name="enumValue">枚举值</param>
        /// <returns>找到的ModbusCoil，未找到返回null</returns>
        public ModbusCoil GetInputCoilByEnum<T>(EnuMcuDeviceType deviceType, T enumValue) where T : Enum
        {
            var ioCode = Extensions.IOEnumExtensions.GetIOCode(enumValue);
            return GetModbusCoilByIOCode(deviceType, ioCode, true);
        }

        /// <summary>
        /// 通过枚举值获取输出线圈(DO)
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="deviceType">设备类型</param>
        /// <param name="enumValue">枚举值</param>
        /// <returns>找到的ModbusCoil，未找到返回null</returns>
        public ModbusCoil GetOutputCoilByEnum<T>(EnuMcuDeviceType deviceType, T enumValue) where T : Enum
        {
            var ioCode = Extensions.IOEnumExtensions.GetIOCode(enumValue);
            return GetModbusCoilByIOCode(deviceType, ioCode, false);
        }

        /// <summary>
        /// 通过枚举值自动判断类型并获取线圈
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="deviceType">设备类型</param>
        /// <param name="enumValue">枚举值</param>
        /// <returns>找到的ModbusCoil，未找到返回null</returns>
        public ModbusCoil GetCoilByEnum<T>(EnuMcuDeviceType deviceType, T enumValue) where T : Enum
        {
            var ioCode = Extensions.IOEnumExtensions.GetIOCode(enumValue);
            var isInput = Extensions.IOEnumExtensions.IsDigitalInput(enumValue);
            return GetModbusCoilByIOCode(deviceType, ioCode, isInput);
        }

        #endregion 通用线圈查找方法

        #endregion 方便通过设备类型、对应DI、DO枚举类型获取线圈实体对象，用于SubSystemStatus\Shuttle\ShuttleSlotStatusManager.cs和SubSystemStatus\Chamber\ChamberSubsystemStatus.cs计算状态

        #region RTZ轴位置访问属性 - 直接访问实时数据

        /// <summary>
        /// T轴当前步进位置值 - 实时数据
        /// </summary>
        public int CurrentTAxisStep => RobotAlarmRegisters.Count > 3 ? RobotAlarmRegisters[3].Combinevalue : 0;

        /// <summary>
        /// R轴当前步进位置值 - 实时数据
        /// </summary>
        public int CurrentRAxisStep => RobotAlarmRegisters.Count > 5 ? RobotAlarmRegisters[5].Combinevalue : 0;

        /// <summary>
        /// Z轴当前步进位置值 - 实时数据
        /// </summary>
        public int CurrentZAxisStep => RobotAlarmRegisters.Count > 7 ? RobotAlarmRegisters[7].Combinevalue : 0;

        /// <summary>
        /// T旋转轴角度值（度） - 实时计算
        /// 转换公式：步进值/100000*360
        /// </summary>
        public double CurrentTAxisDegree => CurrentTAxisStep / 100000.0 * 360.0;

        /// <summary>
        /// R伸缩轴长度值（mm） - 实时计算
        /// 转换公式：L = Sin(步进值/50000*360)*2*208.96
        /// </summary>
        public double CurrentRAxisLength => Math.Sin((CurrentRAxisStep / 50000.0) * (Math.PI / 180) * 360) * 2 * 208.96;

        /// <summary>
        /// Z上下轴高度值（mm） - 实时计算
        /// 转换公式：步进值/1000*5
        /// </summary>
        public double CurrentZAxisHeight => CurrentZAxisStep / 1000.0 * 5.0;

        /// <summary>
        /// 获取RTZ轴位置的组合信息（步进值） - 实时数据
        /// </summary>
        /// <returns>T轴、R轴、Z轴的步进值</returns>
        public (int TAxisStep, int RAxisStep, int ZAxisStep) GetCurrentRTZSteps()
        {
            return (CurrentTAxisStep, CurrentRAxisStep, CurrentZAxisStep);
        }

        /// <summary>
        /// 获取RTZ轴物理位置的组合信息 - 实时计算
        /// </summary>
        /// <returns>T轴角度(度)、R轴长度(mm)、Z轴高度(mm)</returns>
        public (double TAxisDegree, double RAxisLength, double ZAxisHeight) GetCurrentRTZPhysicalValues()
        {
            return (CurrentTAxisDegree, CurrentRAxisLength, CurrentZAxisHeight);
        }

        /// <summary>
        /// 检查RTZ轴位置数据是否有效
        /// </summary>
        public bool IsRTZPositionDataValid => RobotAlarmRegisters.Count > 7;

        #endregion RTZ轴位置访问属性 - 直接访问实时数据

        /// <summary>
        /// 加载设备线圈名称配置
        /// </summary>
        private void LoadDeviceCoilConfig()
        {
            try
            {
                string configPath = App.ConfigHelper.GetConfigFilePath("Configs/IoConfigInfo/McuDeviceCoilName.json");
                if (File.Exists(configPath))
                {
                    string json = File.ReadAllText(configPath);

                    // 使用Newtonsoft.Json反序列化，支持新的JSON结构
                    var stringDictionary = JsonConvert.DeserializeObject<Dictionary<string, DeviceCoilConfig>>(json);

                    // 转换为枚举字典
                    _deviceCoilConfig = new Dictionary<EnuMcuDeviceType, DeviceCoilConfig>();

                    foreach (var kvp in stringDictionary)
                    {
                        if (Enum.TryParse<EnuMcuDeviceType>(kvp.Key, true, out var deviceType))
                        {
                            _deviceCoilConfig[deviceType] = kvp.Value;
                        }
                    }

                    _logger.Info($"成功加载设备线圈名称配置: {configPath}, 包含 {_deviceCoilConfig.Count} 个设备");

                    // 记录每个设备的IO数量
                    foreach (var device in _deviceCoilConfig)
                    {
                        var diCount = device.Value.DiNames?.Count ?? 0;
                        var doCount = device.Value.DONames?.Count ?? 0;
                        _logger.Info($"设备 {device.Key}: DI={diCount}, DO={doCount}");
                    }
                }
                else
                {
                    _logger.Warn($"设备线圈名称配置文件不存在: {configPath}");
                    _deviceCoilConfig = new Dictionary<EnuMcuDeviceType, DeviceCoilConfig>();
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"加载设备线圈名称配置失败: {ex.Message}", ex);
                _deviceCoilConfig = new Dictionary<EnuMcuDeviceType, DeviceCoilConfig>();
            }
        }

        /// <summary>
        /// 初始化线圈集合
        /// </summary>
        private void InitializeCoilCollections()
        {
            // 初始化输入线圈集合
            ShuttleInputCoils = new ObservableCollection<ModbusCoil>();
            RobotInputCoils = new ObservableCollection<ModbusCoil>();
            ChaInputCoils = new ObservableCollection<ModbusCoil>();
            ChbInputCoils = new ObservableCollection<ModbusCoil>();

            // 初始化控制线圈集合
            ShuttleCoils = new ObservableCollection<ModbusCoil>();
            RobotCoils = new ObservableCollection<ModbusCoil>();
            ChaCoils = new ObservableCollection<ModbusCoil>();
            ChbCoils = new ObservableCollection<ModbusCoil>();

            // 为每个设备初始化32个输入线圈，使用int作为循环变量避免类型转换问题
            for (int i = 0; i < CoilCount; i++)
            {
                // 在传入CreateCoil时直接使用枚举类型
                ShuttleInputCoils.Add(CreateCoil((ushort)i, EnuMcuDeviceType.Shuttle));
                RobotInputCoils.Add(CreateCoil((ushort)i, EnuMcuDeviceType.Robot));
                ChaInputCoils.Add(CreateCoil((ushort)i, EnuMcuDeviceType.ChamberA));
                ChbInputCoils.Add(CreateCoil((ushort)i, EnuMcuDeviceType.ChamberB));
            }

            // 为每个设备初始化32个控制线圈
            for (int i = 0; i < CoilCount; i++)
            {
                // 在传入CreateControlCoil时直接使用枚举类型
                ShuttleCoils.Add(CreateControlCoil((ushort)i, EnuMcuDeviceType.Shuttle));
                RobotCoils.Add(CreateControlCoil((ushort)i, EnuMcuDeviceType.Robot));
                ChaCoils.Add(CreateControlCoil((ushort)i, EnuMcuDeviceType.ChamberA));
                ChbCoils.Add(CreateControlCoil((ushort)i, EnuMcuDeviceType.ChamberB));
            }
        }

        /// <summary>
        /// 初始化电机寄存器映射区，包含报警寄存器和位置寄存器
        /// </summary>
        private void InitializeAlarmRegisters()
        {
            // 清空现有集合
            RobotAlarmRegisters.Clear();

            // 初始化电机寄存器映射区
            InitializeMotorRegisters();

            _registers.Values.ToList().ForEach(register =>
            {
                if (register.RegisterType == ModbusRegisterType.Holding)
                {
                    RobotAlarmRegisters.Add(register);
                }
            });

            // 添加报警寄存器
            // for (ushort i = 0; i < AlarmRegisterCount; i++)
            // {
            //     var register = new ModbusRegister
            //     {
            //         Address = (ushort)(AlarmRegisterStartAddress + i),
            //         Name = $"RobotAlarm_{i}",
            //         Title = $"机器人报警寄存器 {i}",
            //         Description = "Robot报警寄存器",
            //         RegisterType = ModbusRegisterType.Holding,
            //         IsWriteable = true,
            //         CombinevalueValueType = ModbusCombineValueType.Register16
            //     };
            //     RobotAlarmRegisters.Add(register);
            // }
        }

        /// <summary>
        /// 初始化电机寄存器映射区
        /// </summary>
        private void InitializeMotorRegisters()
        {
            // 报警寄存器
            AddRegister(0x100, "AlarmT", "当前Alarm(T-Axis)", "T轴旋转报警ErrorCode",
                "", ModbusRegisterType.Holding);
            AddRegister(0x101, "AlarmR", "当前Alarm(R-Axis)", "R轴伸缩报警ErrorCode",
                "", ModbusRegisterType.Holding);
            AddRegister(0x102, "AlarmZ", "当前Alarm(Z-Axis)", "Z轴升降报警ErrorCode",
                "", ModbusRegisterType.Holding);

            // 位置检测寄存器 - T轴 高低位
            AddRegisterPairInt_ABCD(0x103, "DetectPos", "检测位置H(T-Axis)", "T轴高位置检测，32位有符合大端对齐 ABCDEFGH",
                "", ModbusRegisterType.Holding);

            // 位置检测寄存器 - R轴 高低位
            AddRegisterPairInt_ABCD(0x105, "DetectPos", "检测位置H(R-Axis)", "R轴高位置检测，32位有符合大端对齐 ABCDEFGH",
                "", ModbusRegisterType.Holding);

            // 位置检测寄存器 - Z轴 高低位
            AddRegisterPairInt_ABCD(0x107, "DetectPos", "检测位置H(Z-Axis)", "Z轴高位置检测，32位有符合大端对齐 ABCDEFGH",
                "", ModbusRegisterType.Holding);

            // Pin Search - P1点位 高低位
            AddRegisterPairInt_ABCD(0x109, "PinSearch_P1", "Pin Search - P1点位", "Pin Search P1点位置检测，32位有符合大端对齐 ABCDEFGH",
                "", ModbusRegisterType.Holding);

            // Pin Search - P2点位 高低位
            AddRegisterPairInt_ABCD(0x10B, "PinSearch_P2", "Pin Search - P2点位", "Pin Search P2点位置检测，32位有符合大端对齐 ABCDEFGH",
                "", ModbusRegisterType.Holding);

            // AddRegister(0x109, "PinSearch_P1_H", "Pin Search Left", " P1 Pin Search 左边", "", ModbusRegisterType.Holding);
            // AddRegister(0x10A, "PinSearch_P1_L", "Pin Search Right", "P1 Pin Search 右边", "", ModbusRegisterType.Holding);
            // AddRegister(0x10B, "PinSearch_P2_H", "Pin Search Left", "P2 Pin Search 左边", "", ModbusRegisterType.Holding);
            // AddRegister(0x10C, "PinSearch_P2_L", "Pin Search Right", "P2 Pin Search 右边", "", ModbusRegisterType.Holding);

            _logger.Info("电机寄存器映射区初始化完成");
        }

        /// <summary>
        /// 用于转换为32位ABCD Int型（大端对齐）
        /// </summary>
        /// <param name="lowAddress">起始地址</param>
        /// <param name="name">寄存器名称</param>
        /// <param name="title">寄存器标题</param>
        /// <param name="description">寄存器描述</param>
        /// <param name="remark">备注</param>
        /// <param name="type">寄存器类型</param>
        private void AddRegisterPairInt_ABCD(int lowAddress, string name, string title, string description, string remark, ModbusRegisterType type)
        {
            // 高字（存放在低地址）- 与CDAB格式不同，这里高位存储高位值
            _registers[lowAddress] = new ModbusRegister
            {
                Address = (ushort)lowAddress,
                Name = $"{name}_High",
                Title = title,
                Description = description,
                Remark = remark,
                IsWriteable = type == ModbusRegisterType.Holding,
                IsHighWord = true,
                LowRegisterAddress = lowAddress + 1,
                HighRegisterAddress = lowAddress,

                RegisterType = type,
                CombinevalueValueType = ModbusCombineValueType.Register32_Int_ABCD
            };

            // 低字（存放在高地址）- 与CDAB格式不同，这里低位存储低位值
            _registers[lowAddress + 1] = new ModbusRegister
            {
                Address = (ushort)(lowAddress + 1),
                Name = $"{name}_Low",
                Title = title,
                Description = description,
                Remark = remark,
                IsWriteable = type == ModbusRegisterType.Holding,
                IsHighWord = false,
                LowRegisterAddress = lowAddress + 1,
                HighRegisterAddress = lowAddress,

                RegisterType = type,
                CombinevalueValueType = ModbusCombineValueType.Register32_Int_ABCD
            };
        }

        private void AddRegister(int address, string name, string title, string description, string remark, ModbusRegisterType type, bool isWriteable = true)
        {
            _registers[address] = new ModbusRegister
            {
                Address = (ushort)address,
                Name = name,
                Title = title,
                Description = description,
                Remark = remark,
                IsWriteable = isWriteable,
                RegisterType = type
            };
        }

        /// <summary>
        /// 用于转换为32位CDAB Int型
        /// </summary>
        /// <param name="lowAddress">起始地址</param>
        /// <param name="name">寄存器名称</param>
        /// <param name="title">寄存器标题</param>
        /// <param name="description">寄存器描述</param>
        /// <param name="remark">备注</param>
        /// <param name="type">寄存器类型</param>
        private void AddRegisterPairInt(int lowAddress, string name, string title, string description, string remark, ModbusRegisterType type)
        {
            // 高字（存放在低地址）
            _registers[lowAddress] = new ModbusRegister
            {
                Address = (ushort)lowAddress,
                Name = $"{name}_High",
                Title = title,
                Description = description,
                Remark = remark,
                IsWriteable = type == ModbusRegisterType.Holding,
                IsHighWord = true,
                LowRegisterAddress = lowAddress + 1,
                HighRegisterAddress = lowAddress,

                RegisterType = type,
                CombinevalueValueType = ModbusCombineValueType.Register32_Int
            };

            // 低字（存放在高地址）
            _registers[lowAddress + 1] = new ModbusRegister
            {
                Address = (ushort)(lowAddress + 1),
                Name = $"{name}_Low",
                Title = title,
                Description = description,
                Remark = remark,
                IsWriteable = type == ModbusRegisterType.Holding,
                IsHighWord = false,
                LowRegisterAddress = lowAddress + 1,
                HighRegisterAddress = lowAddress,

                RegisterType = type,
                CombinevalueValueType = ModbusCombineValueType.Register32_Int
            };
        }

        /// <summary>
        /// 创建一个线圈对象
        /// </summary>
        /// <param name="address">线圈地址</param>
        /// <param name="deviceType">设备类型</param>
        /// <returns>线圈对象</returns>
        private ModbusCoil CreateCoil(ushort address, EnuMcuDeviceType deviceType)
        {
            string deviceName = deviceType.ToString();
            string name = $"{deviceName}_Coil_{address}";
            string title = $"{deviceName} 线圈 {address}";
            string description = $"{deviceName} 设备的 {address} 号线圈";

            // 尝试从配置中获取线圈名称
            if (_deviceCoilConfig != null && _deviceCoilConfig.TryGetValue(deviceType, out var deviceConfig))
            {
                // 输入线圈 (DI) - 使用地址+1，因为配置文件从1开始编号，而程序从0开始
                if (deviceConfig.DiNames != null && deviceConfig.DiNames.TryGetValue((address + 1).ToString(), out var diInfo))
                {
                    name = $"{deviceName}_DI_{address + 1}";
                    title = diInfo.GetTitle($"DI_{address + 1}");
                    description = diInfo.GetFullDescription(deviceName, address + 1, "输入线圈");
                }
                // 输出线圈 (DO)
                // else if (deviceConfig.DONames != null && deviceConfig.DONames.TryGetValue((address + 1).ToString(), out var doName))
                // {
                //     name = $"{deviceName}_DO_{address + 1}";
                //     title = doName;
                //     description = $"{deviceName} 输出线圈 {address + 1}: {doName}";
                // }
                // // 特殊线圈 (TD等)
                // else
                // {
                //     // 检查是否有其他特殊格式的键，例如TD1、TD2等,实际没有，用于step motor，这里不需要
                //     if (deviceConfig.DONames != null)
                //     {
                //         var specialKeys = deviceConfig.DONames.Keys.Where(k => !int.TryParse(k, out _)).ToList();
                //         foreach (var key in specialKeys)
                //         {
                //             // 假设特殊键使用特定地址范围，例如TD1使用地址30，TD2使用地址31
                //             if ((key == "TD1" && address == 30) || (key == "TD2" && address == 31))
                //             {
                //                 name = $"{deviceName}_{key}";
                //                 title = deviceConfig.DONames[key];
                //                 description = $"{deviceName} 特殊线圈 {key}: {deviceConfig.DONames[key]}";
                //                 break;
                //             }
                //         }
                //     }
                // }
            }

            // 创建ModbusCoil并设置详细信息
            var coil = new ModbusCoil
            {
                Address = address,
                Name = name,
                Title = title,
                Description = description,
                Coilvalue = null,
                Value = 0,
                IsWriteable = false,
                DeviceType = deviceType // 设置设备类型
            };

            // 如果有详细的设备信息，设置到ModbusCoil的新属性中
            if (_deviceCoilConfig != null && _deviceCoilConfig.TryGetValue(deviceType, out var detailedDeviceConfig))
            {
                if (detailedDeviceConfig.DiNames != null && detailedDeviceConfig.DiNames.TryGetValue((address + 1).ToString(), out var diInfo))
                {
                    diInfo.ApplyToModbusCoil(coil);
                }
            }

            return coil;
        }

        /// <summary>
        /// 创建一个控制线圈对象（可读写）
        /// </summary>
        /// <param name="address">线圈地址</param>
        /// <param name="deviceType">设备类型</param>
        /// <returns>控制线圈对象</returns>
        private ModbusCoil CreateControlCoil(ushort address, EnuMcuDeviceType deviceType)
        {
            string deviceName = deviceType.ToString();
            string name = $"{deviceName}_DO_{address}";
            string title = $"{deviceName} 控制线圈 {address}";
            string description = $"{deviceName} 设备的 {address} 号控制线圈";

            // 尝试从配置中获取线圈名称
            if (_deviceCoilConfig != null && _deviceCoilConfig.TryGetValue(deviceType, out var deviceConfig))
            {
                // 输出线圈 (DO) - 使用地址+1，因为配置文件从1开始编号，而程序从0开始
                if (deviceConfig.DONames != null && deviceConfig.DONames.TryGetValue((address + 1).ToString(), out var doInfo))
                {
                    name = $"{deviceName}_DO_{address + 1}";
                    title = doInfo.GetTitle($"DO_{address + 1}");
                    description = doInfo.GetFullDescription(deviceName, address + 1, "输出线圈");
                }
            }

            // 创建ModbusCoil并设置详细信息
            var coil = new ModbusCoil
            {
                Address = address,
                Name = name,
                Title = title,
                Description = description,
                Coilvalue = null,
                Value = 0,
                IsWriteable = true, // 控制线圈是可写的
                DeviceType = deviceType // 设置设备类型
            };

            // 如果有详细的设备信息，设置到ModbusCoil的新属性中
            if (_deviceCoilConfig != null && _deviceCoilConfig.TryGetValue(deviceType, out var detailedDeviceConfig))
            {
                if (detailedDeviceConfig.DONames != null && detailedDeviceConfig.DONames.TryGetValue((address + 1).ToString(), out var doInfo))
                {
                    doInfo.ApplyToModbusCoil(coil);
                }
            }

            return coil;
        }

        /// <summary>
        /// 启动线圈状态监控
        /// </summary>
        public void StartCoilsMonitoring()
        {
            if (_isMonitoring)
                return;

            _logger.Info("启动线圈状态监控");
            _isMonitoring = true;
            _coilMonitorTimer.Start();
        }

        /// <summary>
        /// 停止线圈状态监控
        /// </summary>
        public void StopCoilsMonitoring()
        {
            if (!_isMonitoring)
                return;

            _logger.Info("停止线圈状态监控");
            _isMonitoring = false;
            _coilMonitorTimer.Stop();

            // 取消所有进行中的线圈读取操作
            CancelPendingOperations();
        }

        /// <summary>
        /// 更新报警寄存器状态  优化UpdateAlarmRegistersAsync方法：根据参数， 默认Robot电机寄存器映射区：报警、RTZ位置信息，PinSearch一次性读取，也可以针对PinSearch【读取很快】单独读取
        /// </summary>
        private async Task UpdateAlarmRegistersAsync()
        {
            if (!Robot.IsConnected || RobotAlarmRegisters == null || RobotAlarmRegisters.Count == 0)
                return;

            try
            {
                var client = _clientFactory?.GetOrCreateClient(EnuMcuDeviceType.Robot);
                if (client == null || client.Master == null || !client.IsConnected)
                    return;

                // 使用取消令牌
                ushort[] values = await Task.Run(() =>
                    client.Master.ReadHoldingRegistersAsync(Robot.SlaveId, AlarmRegisterStartAddress, AlarmRegisterCount),
                    _operationCts.Token);

                // 确保UI线程仍然活动
                if (System.Windows.Application.Current == null || System.Windows.Application.Current.Dispatcher == null)
                    return;

                // 更新UI报警寄存器集合
                for (int i = 0; i < values.Length && i < RobotAlarmRegisters.Count; i++)
                {
                    // 在UI线程上更新集合
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        var register = RobotAlarmRegisters[i];
                        register.Value = values[i];

                        // 对于32位的值，需要合并高低位
                        if (i + 1 < values.Length &&
                            (register.CombinevalueValueType == ModbusCombineValueType.Register32_Int ||
                             register.CombinevalueValueType == ModbusCombineValueType.Register32 ||
                             register.CombinevalueValueType == ModbusCombineValueType.Register32_Int_ABCD))
                        {
                            if (register.IsHighWord)
                            {
                                ushort highValue = values[i];
                                ushort lowValue = values[i + 1];

                                if (register.CombinevalueValueType == ModbusCombineValueType.Register32_Int_ABCD)
                                {
                                    // ABCD格式：高位在前，低位在后
                                    int combinedValue = (highValue << 16) | lowValue;
                                    register.Combinevalue = combinedValue;

                                    // 设置低位寄存器的值
                                    if (i + 1 < RobotAlarmRegisters.Count)
                                    {
                                        RobotAlarmRegisters[i + 1].Value = lowValue;
                                    }
                                }
                                else
                                {
                                    // CDAB格式：高位在后，低位在前
                                    int combinedValue = (lowValue << 16) | highValue;
                                    register.Combinevalue = combinedValue;

                                    // 设置低位寄存器的值
                                    if (i + 1 < RobotAlarmRegisters.Count)
                                    {
                                        RobotAlarmRegisters[i + 1].Value = lowValue;
                                    }
                                }

                                // 跳过下一个寄存器，因为已经处理过了
                                i++;
                            }
                        }
                    });
                }
            }
            catch (TaskCanceledException)
            {
                // 正常取消，不记录为错误
                _logger.Debug("读取Robot报警寄存器操作已取消");
            }
            catch (Exception ex)
            {
                _logger.Error($"读取Robot报警寄存器状态时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 启动报警状态监控
        /// </summary>
        public void StartAlarmMonitoring()
        {
            if (_isAlarmMonitoring)
                return;

            _logger.Info("启动报警状态监控");
            _isAlarmMonitoring = true;
            _alarmMonitorTimer.Start();
        }

        /// <summary>
        /// 停止报警状态监控
        /// </summary>
        public void StopAlarmMonitoring()
        {
            if (!_isAlarmMonitoring)
                return;

            _logger.Info("停止报警状态监控");
            _isAlarmMonitoring = false;
            _alarmMonitorTimer.Stop();

            // 取消所有进行中的报警寄存器读取操作
            CancelPendingOperations();
        }

        /// <summary>
        /// 手动更新报警寄存器状态（用于获取最新的Pin Search值等）
        /// </summary>
        public async Task RefreshAlarmRegistersAsync()
        {
            await UpdateAlarmRegistersAsync();
        }

        /// <summary>
        /// 更新所有设备的线圈状态
        /// </summary>
        private async Task UpdateCoilsAsync()
        {
            try
            {
                // 并行更新四个设备的输入线圈状态
                var inputTasks = new List<Task>
                {
                    ReadCoilsAsync(Shuttle, ShuttleInputCoils),
                    ReadCoilsAsync(Robot, RobotInputCoils),
                    ReadCoilsAsync(ChamberA, ChaInputCoils),
                    ReadCoilsAsync(ChamberB, ChbInputCoils)
                };

                // 并行更新四个设备的控制线圈状态
                var controlTasks = new List<Task>
                {
                    ReadControlCoilsAsync(Shuttle, ShuttleCoils),
                    ReadControlCoilsAsync(Robot, RobotCoils),
                    ReadControlCoilsAsync(ChamberA, ChaCoils),
                    ReadControlCoilsAsync(ChamberB, ChbCoils)
                };

                // 等待所有任务完成
                await Task.WhenAll(inputTasks.Concat(controlTasks));
            }
            catch (Exception ex)
            {
                _logger.Error($"更新线圈状态时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 读取设备的线圈状态
        /// </summary>
        /// <param name="device">设备</param>
        /// <param name="coils">线圈集合</param>
        private async Task ReadCoilsAsync(McuDevice device, ObservableCollection<ModbusCoil> coils)
        {
            if (device == null || !device.IsConnected || coils == null || coils.Count == 0)
                return;

            // 检查设备是否支持线圈读取，如果不支持则跳过
            if (!IsCoilReadingSupported(device.DeviceType))
                return;

            try
            {
                // 获取对应的客户端
                var client = _clientFactory?.GetOrCreateClient(device.DeviceType);
                if (client == null || client.Master == null || !client.IsConnected)
                    return;

                // 使用取消令牌
                bool[] coilValues = await Task.Run(() =>
                    client.Master.ReadInputsAsync(device.SlaveId, 0, (ushort)CoilCount),
                    _operationCts.Token);

                // 确保UI线程仍然活动
                if (System.Windows.Application.Current == null || System.Windows.Application.Current.Dispatcher == null)
                    return;

                // 更新UI线圈集合
                for (int i = 0; i < coilValues.Length && i < coils.Count; i++)
                {
                    // 在UI线程上更新集合
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        coils[i].Coilvalue = coilValues[i];
                        coils[i].Value = coilValues[i] ? (ushort)1 : (ushort)0;
                    });
                }
            }
            catch (TaskCanceledException)
            {
                // 正常取消，不记录为错误
                _logger.Debug($"读取 {device.DeviceType} 线圈状态操作已取消");
            }
            catch (Exception ex)
            {
                _logger.Error($"读取 {device.DeviceType} 线圈状态时发生错误: {ex.Message}", ex);

                // 如果是SlaveException且错误码为1(非法功能)，则禁用该设备的线圈读取
                if (ex is NModbus.SlaveException slaveEx && slaveEx.SlaveExceptionCode == 1)
                {
                    SetCoilReadingSupported(device.DeviceType, false);
                }
            }
        }

        /// <summary>
        /// 读取设备的控制线圈状态
        /// </summary>
        /// <param name="device">设备</param>
        /// <param name="controlCoils">控制线圈集合</param>
        private async Task ReadControlCoilsAsync(McuDevice device, ObservableCollection<ModbusCoil> controlCoils)
        {
            if (device == null || !device.IsConnected || controlCoils == null || controlCoils.Count == 0)
                return;

            // 检查设备是否支持线圈读取，如果不支持则跳过
            if (!IsCoilReadingSupported(device.DeviceType))
                return;

            try
            {
                // 获取对应的客户端
                var client = _clientFactory?.GetOrCreateClient(device.DeviceType);
                if (client == null || client.Master == null || !client.IsConnected)
                    return;

                // 使用取消令牌
                bool[] coilValues = await Task.Run(() =>
                    client.Master.ReadCoilsAsync(device.SlaveId, 0, (ushort)CoilCount),
                    _operationCts.Token);

                // 确保UI线程仍然活动
                if (System.Windows.Application.Current == null || System.Windows.Application.Current.Dispatcher == null)
                    return;

                // 更新UI控制线圈集合
                for (int i = 0; i < coilValues.Length && i < controlCoils.Count; i++)
                {
                    // 在UI线程上更新集合
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        controlCoils[i].Coilvalue = coilValues[i];
                        controlCoils[i].Value = coilValues[i] ? (ushort)1 : (ushort)0;
                    });
                }
            }
            catch (TaskCanceledException)
            {
                // 正常取消，不记录为错误
                _logger.Debug($"读取 {device.DeviceType} 控制线圈状态操作已取消");
            }
            catch (Exception ex)
            {
                _logger.Error($"读取 {device.DeviceType} 控制线圈状态时发生错误: {ex.Message}", ex);

                // 控制线圈读取错误不影响输入线圈读取状态
                // 这里可以考虑添加特定的控制线圈读取支持状态
                // if (ex is NModbus.SlaveException slaveEx && slaveEx.SlaveExceptionCode == 1)
                // {
                //     SetCoilReadingSupported(device.DeviceType, false);
                // }
            }
        }

        /// <summary>
        /// 连接所有设备
        /// </summary>
        /// <param name="shuttleIp">Shuttle设备IP</param>
        /// <param name="shuttlePort">Shuttle设备端口</param>
        /// <param name="robotIp">Robot设备IP</param>
        /// <param name="robotPort">Robot设备端口</param>
        /// <param name="chaIp">Cha设备IP</param>
        /// <param name="chaPort">Cha设备端口</param>
        /// <param name="chbIp">Chb设备IP</param>
        /// <param name="chbPort">Chb设备端口</param>
        /// <returns>连接任务</returns>
        public async Task ConnectAllAsync(
            string shuttleIp, int shuttlePort,
            string robotIp, int robotPort,
            string chaIp, int chaPort,
            string chbIp, int chbPort)
        {
            try
            {
                _logger.Info("开始连接所有设备...");

                // 验证ChamberA和ChamberB的连接配置不能相同
                if (chaIp == chbIp && chaPort == chbPort)
                {
                    _logger.Warn($"警告：ChamberA和ChamberB使用相同的连接配置 ({chaIp}:{chaPort})，这可能导致数据混淆");
                    throw new InvalidOperationException("ChamberA和ChamberB不能使用相同的IP地址和端口配置");
                }

                _logger.Info($"设备连接配置:");
                _logger.Info($"  Shuttle: {shuttleIp}:{shuttlePort}");
                _logger.Info($"  Robot: {robotIp}:{robotPort}");
                _logger.Info($"  ChamberA: {chaIp}:{chaPort}");
                _logger.Info($"  ChamberB: {chbIp}:{chbPort}");

                // 并行连接所有设备
                await Task.WhenAll(
                    Shuttle.ConnectAsync(shuttleIp, shuttlePort),
                    Robot.ConnectAsync(robotIp, robotPort),
                    ChamberA.ConnectAsync(chaIp, chaPort),
                    ChamberB.ConnectAsync(chbIp, chbPort)
                );

                _logger.Info("所有设备已连接");

                // 验证设备连接状态
                _logger.Info($"设备连接状态验证:");
                _logger.Info($"  Shuttle: {Shuttle.IsConnected}");
                _logger.Info($"  Robot: {Robot.IsConnected}");
                _logger.Info($"  ChamberA: {ChamberA.IsConnected}");
                _logger.Info($"  ChamberB: {ChamberB.IsConnected}");

                // 初始化设备线圈读取支持状态
                _deviceCoilSupportStatus.Clear();
                SetCoilReadingSupported(EnuMcuDeviceType.Shuttle, true);
                SetCoilReadingSupported(EnuMcuDeviceType.Robot, true);
                SetCoilReadingSupported(EnuMcuDeviceType.ChamberA, true);
                SetCoilReadingSupported(EnuMcuDeviceType.ChamberB, true);

                // 启动线圈和报警状态监控
                StartCoilsMonitoring();
                StartAlarmMonitoring();

                return;
            }
            catch (Exception ex)
            {
                _logger.Error($"连接设备时发生错误: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 断开所有设备连接
        /// </summary>
        /// <returns>断开连接任务</returns>
        public async Task DisconnectAllAsync()
        {
            try
            {
                // 停止状态监控
                StopCoilsMonitoring();
                StopAlarmMonitoring();

                // 取消所有挂起的操作
                CancelPendingOperations();

                // 重置设备线圈读取支持状态
                _deviceCoilSupportStatus.Clear();

                // 并行断开所有设备
                await Task.WhenAll(
                    Shuttle.DisconnectAsync(),
                    Robot.DisconnectAsync(),
                    ChamberA.DisconnectAsync(),
                    ChamberB.DisconnectAsync()
                );

                _logger.Info("所有设备已断开连接");
            }
            catch (Exception ex)
            {
                _logger.Error($"断开设备连接时发生错误: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取所有设备状态
        /// </summary>
        /// <returns>设备状态字典</returns>
        public Dictionary<EnuMcuDeviceType, DeviceStatus> GetAllDeviceStatus()
        {
            return new Dictionary<EnuMcuDeviceType, DeviceStatus>
            {
                { EnuMcuDeviceType.Shuttle, Shuttle.Status },
                { EnuMcuDeviceType.Robot, Robot.Status },
                { EnuMcuDeviceType.ChamberA, ChamberA.Status },
                { EnuMcuDeviceType.ChamberB, ChamberB.Status }
            };
        }

        public async Task<bool> ValidateConnectionAsync()
        {
            if (_modbusMaster == null) return false;

            try
            {
                // 尝试读取一个测试寄存器来验证连接
                await _modbusMaster.ReadHoldingRegistersAsync(1, 0, 1);
                return true;
            }
            catch
            {
                _isConnected = false;
                return false;
            }
        }

        public async Task<TaskStatus> GetCurrentTaskStatusAsync()
        {
            if (!_isConnected || _modbusMaster == null)
            {
                return new TaskStatus { IsRunning = false };
            }

            try
            {
                // 读取任务状态寄存器
                var statusRegisters = await _modbusMaster.ReadHoldingRegistersAsync(1, 100, 2);
                return new TaskStatus
                {
                    IsRunning = statusRegisters[0] == 1,
                    CurrentTask = GetTaskName(statusRegisters[1]),
                    ErrorMessage = null // 如果需要，可以添加错误寄存器的读取
                };
            }
            catch (Exception ex)
            {
                return new TaskStatus
                {
                    IsRunning = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task ConnectAsync(string ipAddress, int port)
        {
            if (_isConnected)
            {
                throw new InvalidOperationException("设备已连接");
            }

            try
            {
                // 创建 Modbus TCP 主站
                var factory = new ModbusFactory();
                _modbusMaster = await Task.Run(() => factory.CreateMaster(new System.Net.Sockets.TcpClient(ipAddress, port)));
                _isConnected = true;
            }
            catch (Exception ex)
            {
                _isConnected = false;
                throw new Exception($"连接失败: {ex.Message}");
            }
        }

        public async Task DisconnectAsync()
        {
            if (!_isConnected || _modbusMaster == null)
            {
                return;
            }

            try
            {
                await Task.Run(() => _modbusMaster.Dispose());
            }
            finally
            {
                _modbusMaster = null;
                _isConnected = false;
            }
        }

        private string GetTaskName(ushort taskCode)
        {
            return taskCode switch
            {
                0 => "空闲",
                1 => "Shuttle移动",
                2 => "ISO阀操作",
                _ => $"未知任务({taskCode})"
            };
        }

        /// <summary>
        /// 检查设备是否支持线圈读取
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>是否支持线圈读取</returns>
        public bool IsCoilReadingSupported(EnuMcuDeviceType deviceType)
        {
            // 如果状态字典中不存在该设备，默认认为支持
            return !_deviceCoilSupportStatus.ContainsKey(deviceType) || _deviceCoilSupportStatus[deviceType];
        }

        /// <summary>
        /// 设置设备线圈读取支持状态
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="isSupported">是否支持</param>
        private void SetCoilReadingSupported(EnuMcuDeviceType deviceType, bool isSupported)
        {
            _deviceCoilSupportStatus[deviceType] = isSupported;
            if (!isSupported)
            {
                _logger.Warn($"设备 {deviceType} 不支持线圈读取功能，已禁用相关功能");
            }
        }

        /// <summary>
        /// 使用命令处理器执行设备命令
        /// </summary>
        /// <typeparam name="TCommand">命令类型</typeparam>
        /// <param name="deviceType">设备类型</param>
        /// <param name="command">要执行的命令</param>
        /// <param name="parameters">命令参数</param>
        /// <returns>命令执行结果</returns>
        public async Task<TaskHandleResult> ExecuteDeviceCommandAsync<TCommand>(EnuMcuDeviceType deviceType, TCommand command, ushort[] parameters = null)
            where TCommand : Enum
        {
            try
            {
                McuDevice device = null;

                // 根据设备类型获取对应的设备
                switch (deviceType)
                {
                    case EnuMcuDeviceType.Shuttle:
                        device = Shuttle;
                        break;

                    case EnuMcuDeviceType.Robot:
                        device = Robot;
                        break;

                    case EnuMcuDeviceType.ChamberA:
                        device = ChamberA;
                        break;

                    case EnuMcuDeviceType.ChamberB:
                        device = ChamberB;
                        break;

                    default:
                        _logger.Error($"未知设备类型: {deviceType}");
                        return TaskHandleResult.Failed;
                }

                if (!device.IsConnected)
                {
                    _logger.Error($"设备 {deviceType} 未连接");
                    return TaskHandleResult.Failed;
                }

                // 创建命令处理器并执行命令
                var handler = _commandFactory.CreateCommandHandler<TCommand>(deviceType);
                var result = await handler.ExecuteCommandAsync(device, command, parameters);

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行设备命令失败: {ex.Message}", ex);
                return TaskHandleResult.Failed;
            }
        }

        /// <summary>
        /// 写入线圈值到设备
        /// </summary>
        /// <param name="coil">要写入的线圈</param>
        /// <param name="value">要写入的值</param>
        public async Task WriteCoilValueAsync(ModbusCoil coil, bool value)
        {
            // 确定线圈所属的设备
            McuDevice device = null;
            EnuMcuDeviceType deviceType = EnuMcuDeviceType.Shuttle; // 默认值

            // 查找线圈所属的设备集合
            if (ShuttleCoils.Contains(coil))
            {
                device = Shuttle;
                deviceType = EnuMcuDeviceType.Shuttle;
            }
            else if (RobotCoils.Contains(coil))
            {
                device = Robot;
                deviceType = EnuMcuDeviceType.Robot;
            }
            else if (ChaCoils.Contains(coil))
            {
                device = ChamberA;
                deviceType = EnuMcuDeviceType.ChamberA;
            }
            else if (ChbCoils.Contains(coil))
            {
                device = ChamberB;
                deviceType = EnuMcuDeviceType.ChamberB;
            }

            if (device != null && device.IsConnected)
            {
                try
                {
                    var client = _clientFactory.GetOrCreateClient(deviceType);
                    if (client?.Master != null)
                    {
                        // 写入线圈值
                        await client.WriteSingleCoilAsync(device.SlaveId, coil.Address, value);
                        _logger.Info($"已写入 {deviceType} 设备的线圈 {coil.Address}: {value}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"写入 {deviceType} 设备的线圈 {coil.Address} 失败: {ex.Message}", ex);
                    // 重新抛出异常以便上层处理
                    throw;
                }
            }
            else
            {
                _logger.Warn($"无法写入线圈值：未找到线圈所属设备或设备未连接");
                throw new InvalidOperationException("设备未连接或无法确定线圈所属设备");
            }
        }

        /// <summary>
        /// 取消所有挂起的操作
        /// </summary>
        private void CancelPendingOperations()
        {
            try
            {
                // 取消旧的操作
                if (_operationCts != null && !_operationCts.IsCancellationRequested)
                {
                    _operationCts.Cancel();
                }

                // 创建新的取消令牌源，用于后续操作
                _operationCts = new CancellationTokenSource();
            }
            catch (Exception ex)
            {
                _logger.Error($"取消挂起操作时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            // 停止所有监控
            StopCoilsMonitoring();
            StopAlarmMonitoring();

            // 取消所有挂起的操作
            try
            {
                _operationCts?.Cancel();
                _operationCts?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.Error($"释放取消令牌源时发生错误: {ex.Message}", ex);
            }

            // 释放定时器
            try
            {
                _coilMonitorTimer?.Dispose();
                _alarmMonitorTimer?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.Error($"释放定时器时发生错误: {ex.Message}", ex);
            }

            _logger.Info("S200 MCU命令服务已释放资源");
        }

        /// <summary>
        /// 检查设备是否支持线圈读取 (接口兼容方法)
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <returns>是否支持线圈读取</returns>
        public bool IsCoilReadingSupported(string deviceName)
        {
            if (string.IsNullOrEmpty(deviceName))
                return false;

            try
            {
                // 尝试将字符串转换为枚举类型
                EnuMcuDeviceType deviceType = Enum.Parse<EnuMcuDeviceType>(deviceName, true);
                return IsCoilReadingSupported(deviceType);
            }
            catch
            {
                // 转换失败，返回false
                return false;
            }
        }
    }

    /// <summary>
    /// MCU设备状态枚举
    /// </summary>
    public enum DeviceStatus
    {
        Disconnected,
        Connected,
        Busy,
        Error
    }

    /// <summary>
    /// 任务处理状态枚举，用于表示Run Flag状态
    /// </summary>
    public enum TaskHandleStatus : ushort
    {
        Idle = 0,
        Trigger = 1,
        Running = 2,
        Complete = 4
    }

    /// <summary>
    /// 任务处理结果枚举
    /// </summary>
    public enum TaskHandleResult
    {
        Success,
        Failed,
        Timeout
    }

    /// <summary>
    /// MCU设备类
    /// </summary>
    public class McuDevice
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(McuDevice));
        private readonly IModbusClientService _modbusClientService;
        private readonly CmdTaskHandler _cmdTaskHandler;
        private string _ipAddress;
        private int _port;
        private readonly byte _slaveId;
        private DateTime _lastConnectedTime;
        private int _totalCommands;
        private int _successfulCommands;
        private int _failedCommands;

        /// <summary>
        /// 设备名称
        /// </summary>
        public EnuMcuDeviceType DeviceType { get; }

        /// <summary>
        /// 设备状态
        /// </summary>
        public DeviceStatus Status { get; private set; } = DeviceStatus.Disconnected;

        /// <summary>
        /// 从站ID
        /// </summary>
        public byte SlaveId => _slaveId;

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected => Status == DeviceStatus.Connected || Status == DeviceStatus.Busy;

        /// <summary>
        /// 根据当前设备名称，获取对应的MCU json命令参数配置，来自Conifgs/CmdParameter目录下，比如： XXXParameter.json解析后的对象列表
        /// </summary>
        public Dictionary<string, CommandConfig> CommandConfigDictionary { get; private set; } = new();

        /// <summary>
        /// 状态变更事件
        /// </summary>
        public event EventHandler<DeviceStatus> StatusChanged;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="enuMcuDeviceType">设备名称</param>
        /// <param name="modbusClientService">Modbus客户端服务</param>
        /// <param name="slaveId">从站ID</param>
        public McuDevice(EnuMcuDeviceType enuMcuDeviceType, IModbusClientService modbusClientService, byte slaveId)
        {
            DeviceType = enuMcuDeviceType;
            _modbusClientService = modbusClientService ?? throw new ArgumentNullException(nameof(modbusClientService));
            _slaveId = slaveId;

            // 初始化任务处理器（根据Modbus通信协议中的寄存器地址配置）
            // 任务处理区起始地址：
            // SF(0), RF(1), RUI1(2), REI(3), CSA(4), CL(5)
            var taskRegisterAddresses = new List<ushort> { 0, 1, 2, 3, 4, 5 };

            // 命令参数区起始地址：
            // 从0x0010开始的32个寄存器用于命令参数
            var cmdParamAddresses = new List<ushort>();
            for (ushort i = (ushort)Golbal.CmdFlagInfoRegLenth; i < Golbal.CmdFlagInfoRegLenth + Golbal.CmdParameterRegLenth; i++)
            {
                cmdParamAddresses.Add(i);
            }

            _cmdTaskHandler = new CmdTaskHandler(taskRegisterAddresses, cmdParamAddresses);

            // 加载设备命令配置列表
            try
            {
                LoadCommandConfigList(DeviceType);
            }
            catch (Exception ex)
            {
                _logger.Warn($"初始化时加载设备 {DeviceType} 命令配置列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 连接设备
        /// </summary>
        /// <param name="ipAddress">IP地址</param>
        /// <param name="port">端口</param>
        /// <returns>连接任务</returns>
        public async Task ConnectAsync(string ipAddress, int port)
        {
            try
            {
                if (IsConnected)
                {
                    if (_ipAddress == ipAddress && _port == port)
                    {
                        _logger.Info($"{DeviceType} 设备已连接到 {ipAddress}:{port}");
                        return;
                    }
                    await DisconnectAsync();
                }

                _ipAddress = ipAddress;
                _port = port;

                _logger.Info($"正在连接 {DeviceType} 设备，IP: {ipAddress}，端口: {port}");

                // 连接Modbus客户端
                var isConnected = await _modbusClientService.ConnectAsync(ipAddress, port);
                // 更新状态
                if (isConnected)
                {
                    UpdateStatus(DeviceStatus.Connected);
                    _lastConnectedTime = DateTime.Now;
                    _logger.Info($"{DeviceType} 设备已成功连接");
                }
                else
                {
                    UpdateStatus(DeviceStatus.Error);
                    _logger.Error($"{DeviceType} 设备连接失败");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus(DeviceStatus.Error);
                _logger.Error($"{DeviceType} 设备连接失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 断开设备连接
        /// </summary>
        /// <returns>断开连接任务</returns>
        public async Task DisconnectAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.Info($"{DeviceType} 设备已断开连接");
                    return;
                }

                _logger.Info($"正在断开 {DeviceType} 设备连接");

                // 断开Modbus客户端连接
                await _modbusClientService.DisconnectAsync();

                // 更新状态
                UpdateStatus(DeviceStatus.Disconnected);

                _logger.Info($"{DeviceType} 设备已成功断开连接");
            }
            catch (Exception ex)
            {
                UpdateStatus(DeviceStatus.Error);
                _logger.Error($"{DeviceType} 设备断开连接失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 泛型执行命令方法，支持不同的命令枚举类型
        /// </summary>
        /// <typeparam name="T">命令枚举类型</typeparam>
        /// <param name="cmdIndex">命令索引</param>
        /// <param name="parameters">命令参数</param>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>执行结果和运行信息</returns>
        public async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> Run<T>(T cmdIndex, List<ushort> parameters, int timeout = 5000) where T : Enum
        {
            string response;
            ushort runInfoValue = 0;
            ushort returnInfoValue = 0;
            _totalCommands++;
            var sw = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                // 快速检查设备连接状态
                if (!IsConnected)
                {
                    string errorMsg = $"{DeviceType} 设备未连接，无法执行命令 {cmdIndex}";
                    _logger.Error(errorMsg);
                    _failedCommands++;
                    return ($"Error: {errorMsg}", runInfoValue, returnInfoValue);
                }

                // 更新设备状态
                UpdateStatus(DeviceStatus.Busy);

                // 记录详细命令信息
                string paramString = parameters != null
                    ? string.Join(",", parameters.Select(p => $"0x{p:X4}"))
                    : "无";
                _logger.Info($"【{DeviceType}】执行命令 {typeof(T).Name}::{cmdIndex}，参数: {paramString}，超时: {timeout}ms");

                // 执行命令 - 使用泛型ExecuteCommandAsync方法
                var result = await _cmdTaskHandler.ExecuteCommandAsync(
                    _modbusClientService.Master,
                    _slaveId,
                    cmdIndex,
                    parameters,
                    timeout);

                // 获取运行信息
                var status = await _cmdTaskHandler.MonitorStatusAsync(_modbusClientService.Master, _slaveId);
                runInfoValue = status.runInfo;
                returnInfoValue = status.returnInfo;

                // 开发调试模式下强制成功
                if (Golbal.IsDevDebug)
                {
                    result = TaskHandleResult.Success;
                }

                // 更新设备状态
                UpdateStatus(DeviceStatus.Connected);

                // 构建响应
                sw.Stop();
                switch (result)
                {
                    case TaskHandleResult.Success:
                        _successfulCommands++;
                        response = $"Success: {DeviceType} 命令 {cmdIndex} 执行成功，运行信息: 0x{runInfoValue:X4}，返回信息: 0x{returnInfoValue:X4}，耗时: {sw.ElapsedMilliseconds}ms";
                        _logger.Info(response);
                        break;

                    case TaskHandleResult.Failed:
                        _failedCommands++;
                        response = $"Failed: {DeviceType} 命令 {cmdIndex} 执行失败，返回信息: 0x{returnInfoValue:X4}，耗时: {sw.ElapsedMilliseconds}ms";
                        _logger.Error(response);
                        break;

                    case TaskHandleResult.Timeout:
                        _failedCommands++;
                        response = $"Timeout: {DeviceType} 命令 {cmdIndex} 执行超时 ({timeout}ms)";
                        _logger.Error(response);
                        break;

                    default:
                        _failedCommands++;
                        response = $"Unknown: {DeviceType} 命令 {cmdIndex} 执行结果未知";
                        _logger.Error(response);
                        break;
                }

                return (response, runInfoValue, returnInfoValue);
            }
            catch (Exception ex)
            {
                sw.Stop();
                _failedCommands++;
                UpdateStatus(DeviceStatus.Error);
                string errorMsg = $"{DeviceType} 命令执行异常: {ex.Message}, 耗时: {sw.ElapsedMilliseconds}ms";
                _logger.Error(errorMsg, ex);
                return ($"Error: {errorMsg}", runInfoValue, returnInfoValue);
            }
        }

        /// <summary>
        /// 重置任务区和命令参数区寄存器
        /// </summary>
        /// <param name="resetValue">重置值</param>
        /// <returns>重置结果</returns>
        public async Task<bool> ResetTaskAsync(ushort resetValue = 0)
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.Warn($"{DeviceType} 设备未连接，无法执行重置");
                    return false;
                }

                UpdateStatus(DeviceStatus.Busy);
                _logger.Info($"正在重置 {DeviceType} 设备");

                // 执行重置
                await _cmdTaskHandler.ResetTaskRegistersAsync(_modbusClientService.Master, _slaveId, resetValue: resetValue);

                UpdateStatus(DeviceStatus.Connected);
                return true;
            }
            catch (Exception ex)
            {
                UpdateStatus(DeviceStatus.Error);
                _logger.Error($"{DeviceType} 设备重置异常: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取设备诊断信息
        /// </summary>
        /// <returns>设备诊断信息</returns>
        public DeviceDiagnostics GetDiagnostics()
        {
            return new DeviceDiagnostics
            {
                DeviceName = DeviceType.ToString(),
                SlaveId = _slaveId,
                Status = Status,
                ConnectionInfo = new ConnectionInfo
                {
                    IpAddress = _ipAddress,
                    Port = _port,
                    IsConnected = IsConnected,
                    LastConnectedTime = _lastConnectedTime
                },
                Statistics = new DeviceStatistics
                {
                    TotalCommands = _totalCommands,
                    SuccessfulCommands = _successfulCommands,
                    FailedCommands = _failedCommands
                }
            };
        }

        /// <summary>
        /// 加载命令配置列表
        /// </summary>
        /// <param name="deviceType">设备名称，如果为null则使用当前设备名称</param>
        /// <returns>命令配置列表</returns>
        public List<CommandConfig> LoadCommandConfigList(EnuMcuDeviceType deviceType)
        {
            //根据枚举设备类型获取对应的配置参数
            var configFileName = ModbusCommandUtility.GetDeviceConfigFileName(DeviceType);

            try
            {
                // 获取配置文件路径
                string configPath = App.ConfigHelper.GetConfigFilePath($"Configs/CmdParameter/{configFileName}");

                // 实例化命令解析器
                var parser = new CmdParameterParser(configPath);

                // 获取所有命令
                var commandNames = parser.GetCommandNames();

                // 清空现有列表
                CommandConfigDictionary.Clear();

                // 遍历所有命令名称，获取对应的命令配置
                foreach (var cmdName in commandNames)
                {
                    try
                    {
                        var config = parser.GetCommand(cmdName);
                        CommandConfigDictionary.Add(cmdName, config);
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"加载命令 {cmdName} 失败: {ex.Message}");
                    }
                }

                _logger.Info($"已从{configPath}加载{CommandConfigDictionary.Count}个{deviceType}设备命令");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载设备{deviceType}命令配置失败: {ex.Message}");
                // 确保列表为空而不是null
                CommandConfigDictionary.Clear();
            }

            return CommandConfigDictionary.Values.ToList();
        }

        /// <summary>
        /// 根据命令索引获取命令配置
        /// </summary>
        /// <param name="cmdIndex">命令索引</param>
        /// <returns>命令配置，如果未找到则返回null</returns>
        public CommandConfig GetCommandConfigByIndex(int cmdIndex)
        {
            // 如果命令列表为空，尝试加载
            if (CommandConfigDictionary.Count == 0)
            {
                try
                {
                    LoadCommandConfigList(DeviceType);
                }
                catch (Exception ex)
                {
                    _logger.Error($"尝试加载命令配置列表失败: {ex.Message}");
                    return null;
                }
            }

            // 查找匹配命令索引的配置
            return CommandConfigDictionary.Values.FirstOrDefault(c => c.CMDIndex == cmdIndex);
        }

        /// <summary>
        /// 根据命令名称获取命令配置
        /// </summary>
        /// <param name="commandName">命令名称</param>
        /// <returns>命令配置，如果未找到则返回null</returns>
        public CommandConfig GetCommandConfigByName(string commandName)
        {
            //根据枚举设备类型获取对应的配置参数
            var configFileName = ModbusCommandUtility.GetDeviceConfigFileName(DeviceType);

            try
            {
                string configPath = App.ConfigHelper.GetConfigFilePath($"Configs/CmdParameter/{configFileName}");
                var parser = new CmdParameterParser(configPath);
                return parser.GetCommand(commandName);
            }
            catch (Exception ex)
            {
                _logger.Error($"获取命令 {commandName} 配置失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 更新设备状态
        /// </summary>
        /// <param name="newStatus">新状态</param>
        private void UpdateStatus(DeviceStatus newStatus)
        {
            if (Status != newStatus)
            {
                Status = newStatus;
                StatusChanged?.Invoke(this, newStatus);
                _logger.Debug($"{DeviceType} 设备状态已更新: {newStatus}");
            }
        }
    }

    /// <summary>
    /// 设备诊断信息
    /// </summary>
    public class DeviceDiagnostics
    {
        public string DeviceName { get; set; }
        public byte SlaveId { get; set; }
        public DeviceStatus Status { get; set; }
        public ConnectionInfo ConnectionInfo { get; set; }
        public DeviceStatistics Statistics { get; set; }
    }

    /// <summary>
    /// 连接信息
    /// </summary>
    public class ConnectionInfo
    {
        public string IpAddress { get; set; }
        public int Port { get; set; }
        public bool IsConnected { get; set; }
        public DateTime LastConnectedTime { get; set; }
    }

    /// <summary>
    /// 设备统计信息
    /// </summary>
    public class DeviceStatistics
    {
        public int TotalCommands { get; set; }
        public int SuccessfulCommands { get; set; }
        public int FailedCommands { get; set; }
    }

    /// <summary>
    /// 命令任务处理器
    /// </summary>
    public class CmdTaskHandler
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(CmdTaskHandler));

        /// <summary>
        /// 任务处理区寄存器地址列表 [SF,RF,RUI1,REI,CSA,CL]
        /// </summary>
        private readonly List<ushort> _taskRegisterAddresses;

        /// <summary>
        /// 命令参数区寄存器地址列表
        /// </summary>
        private readonly List<ushort> _cmdParamAddresses;

        // 当前正在执行的渐变命令标识
        private readonly Dictionary<string, bool> _gradualCommandInProgress = new Dictionary<string, bool>();

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskRegisterAddresses">任务处理区寄存器地址列表</param>
        /// <param name="cmdParamAddresses">命令参数区寄存器地址列表</param>
        public CmdTaskHandler(List<ushort> taskRegisterAddresses, List<ushort> cmdParamAddresses)
        {
            _taskRegisterAddresses = taskRegisterAddresses ?? throw new ArgumentNullException(nameof(taskRegisterAddresses));
            _cmdParamAddresses = cmdParamAddresses ?? throw new ArgumentNullException(nameof(cmdParamAddresses));

            // 验证地址列表长度
            if (taskRegisterAddresses.Count != 6)
                throw new ArgumentException("任务处理区寄存器地址列表必须包含6个地址", nameof(taskRegisterAddresses));

            if (cmdParamAddresses.Count < 1)
                throw new ArgumentException("命令参数区寄存器地址列表至少包含1个地址", nameof(cmdParamAddresses));
        }

        /// <summary>
        /// 在调试模式下执行带Step值的渐变命令
        /// </summary>
        /// <typeparam name="T">命令枚举类型</typeparam>
        /// <param name="master">Modbus主站</param>
        /// <param name="slaveId">从站ID</param>
        /// <param name="cmdIndex">命令索引</param>
        /// <param name="parameters">命令参数</param>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>执行结果</returns>
        public async Task<TaskHandleResult> ExecuteGradualStepCommandAsync<T>(
            IModbusMaster master,
            byte slaveId,
            T cmdIndex,
            List<ushort> parameters,
            int timeout = 5000) where T : Enum
        {
            // 创建命令唯一标识
            string cmdKey = $"{slaveId}_{Convert.ToInt32(cmdIndex)}";

            // 检查是否已有相同的渐变命令在执行
            lock (_gradualCommandInProgress)
            {
                if (_gradualCommandInProgress.TryGetValue(cmdKey, out bool inProgress) && inProgress)
                {
                    _logger.Debug($"已有相同渐变命令正在执行，跳过本次执行: {cmdKey}");
                    return TaskHandleResult.Success; // 直接返回成功
                }

                // 标记当前命令正在执行
                _gradualCommandInProgress[cmdKey] = true;
            }

            try
            {
                // 确认这是带Step值的命令 (至少需要包含高位和低位参数)
                if (parameters == null || parameters.Count < 17)
                {
                    _logger.Warn($"参数不足，无法执行渐变Step命令。使用常规命令执行。");
                    return await ExecuteCommandAsync(master, slaveId, cmdIndex, parameters, timeout);
                }

                // 记录开始时间，用于严格控制总执行时间
                var startTime = DateTime.Now;
                var endTime = startTime.AddMilliseconds(timeout);

                // 获取目标Step值（从参数中的15和16位置获取高低位）
                int targetSteps = ((int)parameters[15] << 16) | parameters[16];

                // 确定当前命令类型，以便更新正确的位置寄存器
                ushort positionRegisterAddress = 0;
                string axisName = "unknown";

                // 将枚举值转换为对应的整数值
                int cmdIndexValue = Convert.ToInt32(cmdIndex);
                string cmdName = Enum.GetName(cmdIndex.GetType(), cmdIndex) ?? string.Empty;

                // 根据命令类型确定要更新的寄存器地址
                if (cmdName.Contains("Move_T_Axis") || cmdIndexValue == 3) // T轴
                {
                    positionRegisterAddress = 0x103; // T轴位置寄存器地址
                    axisName = "T轴";
                }
                else if (cmdName.Contains("Move_R_Axis") || cmdIndexValue == 4) // R轴
                {
                    positionRegisterAddress = 0x105; // R轴位置寄存器地址
                    axisName = "R轴";
                }
                else if (cmdName.Contains("Move_Z_Axis") || cmdIndexValue == 5) // Z轴
                {
                    positionRegisterAddress = 0x107; // Z轴位置寄存器地址
                    axisName = "Z轴";
                }
                else
                {
                    _logger.Warn($"未识别的轴命令: {cmdName}({cmdIndexValue})，无法确定正确的位置寄存器，使用常规命令执行");
                    return TaskHandleResult.Failed;
                    //return await ExecuteCommandAsync(master, slaveId, cmdIndex, parameters, timeout);// 不能再执行这个命令，跳出这个循环后面会执行
                }

                // 读取当前轴的实际位置值
                int currentStep = 0;
                try
                {
                    // 读取位置寄存器的高低位（ABCD格式，高位在低地址）
                    ushort[] posValues = await Task.Run(() => master.ReadHoldingRegisters(
                        slaveId,
                        positionRegisterAddress,
                        2)); // 读取2个字，高低位

                    if (posValues != null && posValues.Length == 2)
                    {
                        // 按照ABCD格式组合成32位整数
                        currentStep = (posValues[0] << 16) | posValues[1];
                        _logger.Debug($"成功读取{axisName}当前位置: {currentStep}");
                    }
                    else
                    {
                        _logger.Warn($"读取{axisName}当前位置失败，使用0作为初始位置");
                        currentStep = 0;
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"读取{axisName}当前位置时发生错误: {ex.Message}，使用0作为初始位置");
                    currentStep = 0;
                }

                _logger.Debug($"调试模式: 命令 {axisName} {cmdName}({cmdIndexValue}) - 从当前值 {currentStep} 渐变到目标值 {targetSteps}, " +
                    $"变化量: {targetSteps - currentStep}, 超时: {timeout}ms, 位置寄存器: 0x{positionRegisterAddress:X4}, " +
                    $"预计结束时间: {endTime:HH:mm:ss.fff}");

                // 计算每次增量和时间间隔
                int totalSteps = Math.Abs(targetSteps - currentStep);
                if (totalSteps == 0)
                {
                    _logger.Debug($"{axisName}当前值与目标值相同，无需渐变");
                    // 不执行任何操作，让主命令流正常执行
                    return TaskHandleResult.Success;
                }

                int direction = targetSteps > currentStep ? 1 : -1;

                // 计算步骤数和每步间隔时间 - 根据总步数和超时时间动态调整
                int stepCount;

                // 根据移动距离动态调整步骤数
                if (totalSteps <= 10)
                {
                    stepCount = 5; // 变化小时使用较少步骤
                }
                else if (totalSteps <= 100)
                {
                    stepCount = 10;
                }
                else if (totalSteps <= 1000)
                {
                    stepCount = 20;
                }
                else if (totalSteps <= 10000)
                {
                    stepCount = 40;
                }
                else if (totalSteps <= 100000)
                {
                    stepCount = 60;
                }
                else
                {
                    stepCount = 80; // 最大步骤数
                }

                // 确保超时时间足够（每步至少需要30ms）
                int minTimeNeeded = stepCount * 30;
                if (timeout < minTimeNeeded)
                {
                    // 如果超时时间不足，减少步骤数但保持较高的Smoth端度
                    stepCount = Math.Max(5, timeout / 30);
                    _logger.Debug($"超时时间({timeout}ms)不足以完成{stepCount}步，已调整为{stepCount}步");
                }

                // 计算每步大小，确保有明显变化
                int stepSize = totalSteps / stepCount;
                if (stepSize < 1) stepSize = 1;

                // 剩余要完成的步数，随执行过程更新
                int remainingSteps = stepCount;

                // 上次执行的步骤值
                int lastValue = currentStep;

                // 计算每步基本间隔时间（ms）
                int baseIntervalMs = timeout / stepCount;

                // 逐步接近目标值
                for (int i = 1; i <= stepCount; i++)
                {
                    // 检查是否还有足够时间完成剩余步骤
                    var now = DateTime.Now;
                    var remainingTime = (endTime - now).TotalMilliseconds;

                    // 如果剩余时间不足，直接跳到最终值
                    if (remainingTime <= 0 || remainingTime < remainingSteps * 30) // 至少预留每步30ms
                    {
                        _logger.Debug($"时间不足，直接跳至目标值! 剩余时间: {remainingTime}ms, 剩余步骤: {remainingSteps}");

                        // 直接设置为目标值并退出循环
                        await UpdateStepValue(master, slaveId, cmdIndexValue, targetSteps, positionRegisterAddress);
                        _logger.Debug($"{axisName}渐变提前完成，最终位置: {targetSteps}，用时: {(DateTime.Now - startTime).TotalMilliseconds:0}ms");
                        break;
                    }

                    // 计算当前步骤应该设置的值
                    int currentValue;
                    if (i == stepCount)
                    {
                        // 最后一步，确保精确到达目标值
                        currentValue = targetSteps;
                    }
                    else
                    {
                        // 计算当前步骤的目标值 - 确保均匀分布
                        double progress = (double)i / stepCount;
                        currentValue = (int)(currentStep + (totalSteps * progress * direction));

                        // 确保值在合理范围内
                        if ((direction > 0 && currentValue > targetSteps) ||
                            (direction < 0 && currentValue < targetSteps))
                        {
                            currentValue = targetSteps;
                        }
                    }

                    // 动态调整每步间隔，确保均匀分布在剩余时间内
                    int intervalMs = (int)(remainingTime / remainingSteps);
                    // 控制每步最小和最大间隔，确保平稳
                    intervalMs = Math.Max(20, Math.Min(intervalMs, baseIntervalMs * 2));

                    _logger.Debug($"步骤 {i}/{stepCount}: 设置{axisName}值为 {currentValue}，" +
                        $"间隔: {intervalMs}ms，剩余时间: {remainingTime:0}ms，剩余步骤: {remainingSteps}");

                    // 更新寄存器和位置显示
                    await UpdateStepValue(master, slaveId, cmdIndexValue, currentValue, positionRegisterAddress);

                    // 减少剩余步数
                    remainingSteps--;

                    // 记录上次设置的值
                    lastValue = currentValue;

                    // 如果不是最后一步，等待指定时间间隔
                    if (i < stepCount)
                    {
                        // 计算安全的延迟时间，确保不会超出剩余时间
                        var delayTime = Math.Min(intervalMs, (int)remainingTime - 30);
                        if (delayTime > 0)
                        {
                            await Task.Delay(delayTime);
                        }
                    }
                }

                // 确保最后设置的是精确的目标值
                if (lastValue != targetSteps)
                {
                    _logger.Debug($"确保最终精确设置目标值: {targetSteps}");
                    await UpdateStepValue(master, slaveId, cmdIndexValue, targetSteps, positionRegisterAddress);

                    // 加入短暂延迟确保最终值已稳定写入
                    await Task.Delay(50);
                }

                var totalTime = (DateTime.Now - startTime).TotalMilliseconds;
                _logger.Debug($"{axisName}渐变完成，最终位置: {targetSteps}，用时: {totalTime:0}ms，超时设定: {timeout}ms，{(totalTime <= timeout ? "在时间内完成" : "已超时")}");

                // 渐变命令完成后，不再执行额外的命令，避免值不稳定
                return TaskHandleResult.Success;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行渐变命令失败: {ex.Message}", ex);
                return TaskHandleResult.Failed;
            }
            finally
            {
                // 释放命令锁，允许后续相同命令执行
                lock (_gradualCommandInProgress)
                {
                    _gradualCommandInProgress[cmdKey] = false;
                }
            }
        }

        /// <summary>
        /// 更新步进值到位置显示
        /// </summary>
        private async Task UpdateStepValue(IModbusMaster master, byte slaveId, int cmdIndexValue, int stepValue, ushort positionRegisterAddress)
        {
            // 计算高低字节
            ushort highWord = (ushort)((stepValue >> 16) & 0xFFFF);
            ushort lowWord = (ushort)(stepValue & 0xFFFF);

            try
            {
                // 更新位置检测寄存器，使UI界面更新显示
                // 位置值用ABCD格式存储(大端序)，高位在前(positionRegisterAddress)，低位在后(positionRegisterAddress+1)
                await Task.Run(() => master.WriteMultipleRegisters(
                    slaveId,
                    positionRegisterAddress,
                    new ushort[] {
                        highWord, // 高位字
                        lowWord   // 低位字
                    }));
            }
            catch (Exception ex)
            {
                _logger.Error($"更新步进值失败: {ex.Message}", ex);
                // 不抛出异常，允许过程继续
            }
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="master">Modbus主站</param>
        /// <param name="slaveId">从站ID</param>
        /// <param name="shuttleCmdIndex">命令索引</param>
        /// <param name="parameters">命令参数</param>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>执行结果</returns>
        public async Task<TaskHandleResult> ExecuteCommandAsync(IModbusMaster master, byte slaveId, EnuShuttleCmdIndex shuttleCmdIndex, List<ushort> parameters, int timeout = 5000)
        {
            try
            {
                /*
                指令执行过程:
                    1.向非handle区写入指令数据及参数(例: 向0x0030地址写入CMD Index, Parameter)
                    2.向Task Handle1区域写入代表触发的状态及更新指令所在地址及长度信息
                    3.一直轮询读取Task Handle1, 在RF_Reg值为0x0004时任务执行完成, 并且RI_Reg值有效
                (在ReadComplete Run位置位读取之后会将当前Task Handle的所有寄存器都清零)

                    4.若在Task Handle1执行期间有新的动作需要执行, 可以在未使用的非Handle区域写入
                指令及参数, 再将未使用的Task Handle写入触发状态及地址和长度信息。
                */

                //  1.准备命令数据（索引+参数）
                var cmdData = new List<ushort> { (ushort)shuttleCmdIndex };
                if (parameters != null && parameters.Count > 0)
                {
                    cmdData.AddRange(parameters);
                }
                var cmdDataCount = cmdData.Count;

                // 参数校验和处理
                if (cmdDataCount > _cmdParamAddresses.Count)
                {
                    _logger.Error($"参数数量({cmdData.Count})超过寄存器数量({_cmdParamAddresses.Count})，将截断多余的参数。命令索引:{shuttleCmdIndex}, 原始参数:{string.Join(",", cmdData)}");
                    cmdData = cmdData.Take(_cmdParamAddresses.Count).ToList();
                    _logger.Info($"截断后的参数:{string.Join(",", cmdData)}");
                }

                // 2. 单次网络操作：将命令数据写入寄存器
                await Task.Run(() => master.WriteMultipleRegisters(slaveId, _cmdParamAddresses[0], cmdData.ToArray()));

                // 3. 单次网络操作：写入任务控制信息并触发命令执行
                var taskControlData = new ushort[]
                {
                    (ushort)TaskHandleStatus.Trigger, // Status Flag = 1 (Trigger)，触发标志
                    0,                               // Run Flag = 0
                    0,                               // Run Info = 0
                    0,                               // Return Info = 0
                    (ushort)cmdDataCount             // CMD Len = 参数数量
                };

                await Task.Run(() => master.WriteMultipleRegisters(
                    slaveId,
                    _taskRegisterAddresses[0],
                    taskControlData));

                // 4. 使用指数退避策略等待命令执行完成
                var startTime = DateTime.Now;
                int delayMs = 10; // 初始延迟
                const int maxDelayMs = 100; // 最大延迟
                ushort currentStatus = 0;

                while (currentStatus != (ushort)TaskHandleStatus.Complete)
                {
                    // 读取当前状态
                    currentStatus = await Task.Run(() => master.ReadHoldingRegisters(slaveId, _taskRegisterAddresses[1], 1)[0]);

                    // 检查是否超时
                    if ((DateTime.Now - startTime).TotalMilliseconds > timeout)
                    {
                        if (!Golbal.IsDevDebug)
                        {
                            _logger.Warn($"等待命令 {shuttleCmdIndex} 执行超时({timeout}ms)");
                        }
                        return TaskHandleResult.Timeout;
                    }

                    // 指数退避延迟
                    await Task.Delay(delayMs);
                    delayMs = Math.Min(delayMs * 2, maxDelayMs);
                }

                // 任务执行成功
                return TaskHandleResult.Success;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行命令失败: {ex.Message}", ex);
                return TaskHandleResult.Failed;
            }
        }

        /// <summary>
        /// 监控任务状态
        /// </summary>
        /// <param name="master">Modbus主站</param>
        /// <param name="slaveId">从站ID</param>
        /// <returns>运行信息和返回信息</returns>
        public async Task<(TaskHandleStatus runFlagStatus, ushort runInfo, ushort returnInfo)> MonitorStatusAsync(IModbusMaster master, byte slaveId)
        {
            try
            {
                // 读取任务处理区寄存器
                var taskRegisters = await Task.Run(() => master.ReadHoldingRegisters(
                    slaveId,
                    _taskRegisterAddresses[0],
                    (ushort)_taskRegisterAddresses.Count));

                // 解析寄存器值
                var runFlag = (TaskHandleStatus)taskRegisters[1];  // Run Flag
                var runInfo = taskRegisters[2];                    // Run Info
                var returnInfo = taskRegisters[3];                 // Return Info

                return (runFlag, runInfo, returnInfo);
            }
            catch (Exception ex)
            {
                _logger.Error($"监控任务状态异常: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 重置任务区和命令参数区寄存器
        /// </summary>
        /// <param name="master">Modbus主站</param>
        /// <param name="slaveId">从站ID</param>
        /// <param name="resetValue">重置值</param>
        /// <returns>重置任务</returns>
        public async Task ResetTaskRegistersAsync(IModbusMaster master, byte slaveId, ushort resetValue = 0)
        {
            try
            {
                var registersCount = Golbal.CmdFlagInfoRegLenth + Golbal.CmdParameterRegLenth;
                var taskRegisters = new ushort[registersCount];
                for (int i = 0; i < registersCount; i++)
                {
                    taskRegisters[i] = resetValue;
                }

                await Task.Run(() => master.WriteMultipleRegisters(
                    slaveId,
                    _taskRegisterAddresses[0],
                    taskRegisters
                    ));

                // 清空任务处理区寄存器
                // await Task.Run(() => master.WriteMultipleRegisters(
                //     slaveId,
                //     _taskRegisterAddresses[0],
                //     new ushort[] { 0, 0, 0, 0, 0, 0 }));

                // 清空命令参数区寄存器
                // await Task.Run(() => master.WriteMultipleRegisters(
                //     slaveId,
                //     _cmdParamAddresses[0],
                //     new ushort[_cmdParamAddresses.Count]));

                // _logger.Info("任务重置成功");
            }
            catch (Exception ex)
            {
                _logger.Error($"任务重置失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 清空指定范围的寄存器
        /// </summary>
        /// <param name="master">Modbus主站</param>
        /// <param name="slaveId">从站ID</param>
        /// <param name="registerCount">寄存器数量</param>
        /// <returns>执行结果</returns>
        public async Task<TaskHandleResult> ExecuteClearAllAsync(IModbusMaster master, byte slaveId, int registerCount = 64)
        {
            try
            {
                // 清空指定范围的寄存器
                await Task.Run(() => master.WriteMultipleRegisters(slaveId, 0, new ushort[registerCount]));
                return TaskHandleResult.Success;
            }
            catch (Exception ex)
            {
                _logger.Error($"清空寄存器失败: {ex.Message}", ex);
                return TaskHandleResult.Failed;
            }
        }

        /// <summary>
        /// 泛型执行命令方法，支持不同的命令枚举类型
        /// </summary>
        /// <typeparam name="T">命令枚举类型</typeparam>
        /// <param name="master">Modbus主站</param>
        /// <param name="slaveId">从站ID</param>
        /// <param name="cmdIndex">命令索引</param>
        /// <param name="parameters">命令参数</param>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>执行结果</returns>
        public async Task<TaskHandleResult> ExecuteCommandAsync<T>(IModbusMaster master, byte slaveId, T cmdIndex, List<ushort> parameters, int timeout = 5000) where T : Enum
        {
            try
            {
                // 将枚举值转换为对应的整数值 - 在方法开始处就声明和初始化
                int cmdIndexValue = Convert.ToInt32(cmdIndex);
                var cmdType = cmdIndex.GetType();
                string cmdName = Enum.GetName(cmdType, cmdIndex) ?? string.Empty;

                // 在调试模式下，检查是否需要执行渐变命令
                if (Golbal.IsDevDebug && parameters != null && parameters.Count >= 17)
                {
                    // 检查命令是否是要处理的移动位置的3个命令之一
                    bool isAxisPositionCmd =
                        cmdName.Contains("Move_T_Axis") || cmdName.Contains("Move_R_Axis") || cmdName.Contains("Move_Z_Axis") ||
                        ((cmdIndexValue == 3 || cmdIndexValue == 4 || cmdIndexValue == 5) && cmdType.Name.Contains("Robot"));

                    if (isAxisPositionCmd)
                    {
                        _logger.Debug($"调试模式: 检测到轴位置命令 {cmdName}({cmdIndexValue})，开始执行Step值渐变");

                        // 启动渐变命令后台执行
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await ExecuteGradualStepCommandAsync(master, slaveId, cmdIndex, parameters, timeout);
                            }
                            catch (Exception ex)
                            {
                                _logger.Error($"后台执行渐变命令失败: {ex.Message}", ex);
                            }
                        });

                        // 添加短暂延迟，让渐变命令先开始执行
                        await Task.Delay(50);

                        // 继续执行原始命令流程，确保命令被正确处理
                    }
                }

                // 1. 准备命令数据（索引+参数）
                var cmdData = new List<ushort> { (ushort)cmdIndexValue };
                if (parameters != null && parameters.Count > 0)
                {
                    cmdData.AddRange(parameters);
                }
                var cmdDataCount = cmdData.Count;

                // 参数校验和处理
                if (cmdDataCount > _cmdParamAddresses.Count)
                {
                    _logger.Error($"参数数量({cmdData.Count})超过寄存器数量({_cmdParamAddresses.Count})，将截断多余的参数。命令索引:{cmdIndex}, 原始参数:{string.Join(",", cmdData)}");
                    cmdData = cmdData.Take(_cmdParamAddresses.Count).ToList();
                    _logger.Info($"截断后的参数:{string.Join(",", cmdData)}");
                }

                // 2. 单次网络操作：将命令数据写入寄存器
                // await Task.Run(() => master.WriteMultipleRegisters(
                //     slaveId,
                //     _cmdParamAddresses[0],
                //     cmdData.ToArray()));

                // 3. 单次网络操作：写入任务控制信息并触发命令执行
                // 构造长度为16的数组，前5项为控制数据，后面补0
                var taskControlData = new ushort[16];
                taskControlData[0] = (ushort)TaskHandleStatus.Trigger; // Status Flag = 1 (Trigger)
                taskControlData[1] = 0;                                // Run Flag = 0
                taskControlData[2] = 0;                                // Run Info = 0
                taskControlData[3] = 0;                                // Return Info = 0
                taskControlData[4] = (ushort)cmdDataCount;           // CMD Len = 参数数量

                // var taskControlData = new ushort[]
                // {
                //     (ushort)TaskHandleStatus.Trigger, // Status Flag = 1 (Trigger)，触发标志
                //     0,                               // Run Flag = 0
                //     0,                               // Run Info = 0
                //     0,                               // Return Info = 0
                //     (ushort)cmdDataCount           // CMD Len = 参数数量
                //
                // };

                // 合并taskControl和cmdData
                var allData = new List<ushort>(taskControlData);
                allData.AddRange(cmdData);

                // 数据验证和日志记录
                if (!ValidateCommandData(allData, cmdIndex, slaveId))
                {
                    _logger.Error($"命令数据验证失败，中止执行。命令: {cmdIndex}, 从站: {slaveId}");
                    return TaskHandleResult.Failed;
                }

                // 执行写入操作，包含重试机制
                await ExecuteWriteWithRetryAsync(master, slaveId, _taskRegisterAddresses[0], allData.ToArray(), cmdIndex);

                // 4. 使用指数退避策略等待命令执行完成
                var startTime = DateTime.Now;
                int delayMs = 10; // 初始延迟
                const int maxDelayMs = 100; // 最大延迟

                while (true)
                {
                    // 检查是否超时
                    if ((DateTime.Now - startTime).TotalMilliseconds > timeout)
                    {
                        if (!Golbal.IsDevDebug)
                        {
                            _logger.Warn($"等待命令 {cmdIndex} 执行超时({timeout}ms)");
                        }
                        return TaskHandleResult.Timeout;
                    }

                    try
                    {
                        // 获取当前任务状态
                        var (status, runInfo, returnInfo) = await MonitorStatusAsync(master, slaveId);

                        // 根据Run Flag判断执行状态
                        switch (status)
                        {
                            case TaskHandleStatus.Complete:  // 任务完成
                                _logger.Debug($"任务完成: RunInfo={runInfo}, ReturnInfo={returnInfo}");
                                // 返回信息为0表示成功，非0表示失败
                                return returnInfo == 0 ? TaskHandleResult.Success : TaskHandleResult.Failed;

                            case TaskHandleStatus.Idle:  // 空闲状态，可能是执行出错
                                if (returnInfo != 0)
                                {
                                    _logger.Warn($"任务执行失败: ReturnInfo={returnInfo}");
                                    return TaskHandleResult.Failed;
                                }
                                break;
                        }

                        // 指数退避延迟
                        await Task.Delay(delayMs);
                        delayMs = Math.Min(delayMs * 2, maxDelayMs);
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"等待任务完成异常: {ex.Message}", ex);
                        return TaskHandleResult.Failed;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"执行命令失败: {ex.Message}", ex);
                return TaskHandleResult.Failed;
            }
        }

        /// <summary>
        /// 验证命令数据的有效性
        /// </summary>
        /// <param name="allData">要发送的完整数据</param>
        /// <param name="cmdIndex">命令索引</param>
        /// <param name="slaveId">从站ID</param>
        /// <returns>验证是否通过</returns>
        private bool ValidateCommandData<T>(List<ushort> allData, T cmdIndex, byte slaveId) where T : Enum
        {
            try
            {
                // 基本长度检查
                if (allData == null || allData.Count == 0)
                {
                    _logger.Error($"命令数据为空。命令: {cmdIndex}, 从站: {slaveId}");
                    return false;
                }

                // 检查数据长度是否合理（不超过最大寄存器数量）
                const int maxRegisterCount = 100; // 根据设备规格调整
                if (allData.Count > maxRegisterCount)
                {
                    _logger.Error($"命令数据长度({allData.Count})超过最大允许值({maxRegisterCount})。命令: {cmdIndex}, 从站: {slaveId}");
                    return false;
                }

                // 检查任务控制数据格式
                if (allData.Count < 5)
                {
                    _logger.Error($"命令数据长度({allData.Count})小于最小要求(5)。命令: {cmdIndex}, 从站: {slaveId}");
                    return false;
                }

                // 验证任务控制标志
                if (allData[0] != (ushort)TaskHandleStatus.Trigger)
                {
                    _logger.Error($"任务触发标志错误，期望: {(ushort)TaskHandleStatus.Trigger}, 实际: {allData[0]}。命令: {cmdIndex}, 从站: {slaveId}");
                    return false;
                }

                // 验证命令长度字段
                ushort declaredLength = allData[4];
                int actualCmdLength = allData.Count - 16; // 减去任务控制数据长度
                if (declaredLength != actualCmdLength)
                {
                    _logger.Warn($"声明的命令长度({declaredLength})与实际长度({actualCmdLength})不匹配。命令: {cmdIndex}, 从站: {slaveId}");
                }

                // 记录详细的数据内容用于调试
                _logger.Debug($"命令数据验证通过。命令: {cmdIndex}, 从站: {slaveId}, 数据长度: {allData.Count}");
                _logger.Debug($"数据内容: [{string.Join(", ", allData.Select(x => $"0x{x:X4}"))}]");

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"验证命令数据时发生异常: {ex.Message}。命令: {cmdIndex}, 从站: {slaveId}", ex);
                return false;
            }
        }

        /// <summary>
        /// 执行带重试机制的写入操作
        /// </summary>
        /// <param name="master">Modbus主站</param>
        /// <param name="slaveId">从站ID</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="data">要写入的数据</param>
        /// <param name="cmdIndex">命令索引</param>
        /// <returns>异步任务</returns>
        private async Task ExecuteWriteWithRetryAsync<T>(IModbusMaster master, byte slaveId, ushort startAddress, ushort[] data, T cmdIndex) where T : Enum
        {
            const int maxRetries = 3;
            const int baseDelayMs = 100;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    _logger.Debug($"尝试写入寄存器 (第{attempt}次): 从站{slaveId}, 地址0x{startAddress:X4}, 长度{data.Length}");

                    await Task.Run(() => master.WriteMultipleRegisters(slaveId, startAddress, data));

                    _logger.Debug($"寄存器写入成功 (第{attempt}次尝试)");
                    return; // 成功，退出重试循环
                }
                catch (NModbus.SlaveException slaveEx)
                {
                    string errorDetail = GetSlaveExceptionDetail(slaveEx);
                    _logger.Error($"Modbus从站异常 (第{attempt}次尝试): {errorDetail}。命令: {cmdIndex}, 从站: {slaveId}");

                    // 对于特定的异常码，不进行重试
                    if (slaveEx.SlaveExceptionCode == 8) // 一致性检查失败
                    {
                        _logger.Error($"数据一致性检查失败，不进行重试。数据: [{string.Join(", ", data.Select(x => $"0x{x:X4}"))}]");
                        throw; // 重新抛出异常
                    }

                    if (attempt == maxRetries)
                    {
                        _logger.Error($"达到最大重试次数({maxRetries})，写入失败");
                        throw; // 重新抛出异常
                    }

                    // 指数退避延迟
                    int delayMs = baseDelayMs * (int)Math.Pow(2, attempt - 1);
                    _logger.Info($"等待 {delayMs}ms 后进行第{attempt + 1}次重试");
                    await Task.Delay(delayMs);
                }
                catch (Exception ex)
                {
                    _logger.Error($"写入寄存器时发生未知异常 (第{attempt}次尝试): {ex.Message}。命令: {cmdIndex}, 从站: {slaveId}", ex);

                    if (attempt == maxRetries)
                    {
                        _logger.Error($"达到最大重试次数({maxRetries})，写入失败");
                        throw; // 重新抛出异常
                    }

                    // 对于未知异常，使用较短的延迟
                    await Task.Delay(baseDelayMs);
                }
            }
        }

        /// <summary>
        /// 获取Modbus从站异常的详细描述
        /// </summary>
        /// <param name="slaveEx">从站异常</param>
        /// <returns>异常详细描述</returns>
        private string GetSlaveExceptionDetail(NModbus.SlaveException slaveEx)
        {
            string exceptionCodeDesc = slaveEx.SlaveExceptionCode switch
            {
                1 => "非法功能码",
                2 => "非法数据地址",
                3 => "非法数据值",
                4 => "从站设备故障",
                5 => "确认",
                6 => "从站设备忙",
                8 => "扩展文件区域一致性检查失败",
                10 => "网关路径不可用",
                11 => "网关目标设备响应失败",
                _ => $"未知异常码({slaveEx.SlaveExceptionCode})"
            };

            return $"功能码: {slaveEx.FunctionCode}, 异常码: {slaveEx.SlaveExceptionCode} - {exceptionCodeDesc}";
        }
    }

    public class TaskStatus
    {
        public bool IsRunning { get; set; }
        public string CurrentTask { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 设备线圈配置类
    /// </summary>
    public class DeviceCoilConfig
    {
        public Dictionary<string, IoDeviceInfo> DiNames { get; set; }
        public Dictionary<string, IoDeviceInfo> DONames { get; set; }
    }

    /// <summary>
    /// IO设备信息类
    /// </summary>
    public class IoDeviceInfo
    {
        /// <summary>
        /// 设备名称
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; } = "";

        /// <summary>
        /// IO代码 (如: RDI1, SDI1, PDI1等)
        /// </summary>
        [JsonProperty("ioCode")]
        public string IoCode { get; set; } = "";

        /// <summary>
        /// MCU IO映射 (如: SDI_26/RDI_26, PDI_9等)
        /// </summary>
        [JsonProperty("mcuIo")]
        public string McuIo { get; set; } = "";

        /// <summary>
        /// IO类型 (DI, DO)
        /// </summary>
        [JsonProperty("ioType")]
        public string IoType { get; set; } = "";

        /// <summary>
        /// 传感器类型 (如: optic sensor NPN, switch等)
        /// </summary>
        [JsonProperty("sensorType")]
        public string SensorType { get; set; } = "";

        /// <summary>
        /// 控制类型 (如: EV, position control等)
        /// </summary>
        [JsonProperty("controlType")]
        public string ControlType { get; set; } = "";

        /// <summary>
        /// 备注信息 (如: no wafer:0 wafer:1, enable:1等)
        /// </summary>
        [JsonProperty("remark")]
        public string Remark { get; set; } = "";
    }
}