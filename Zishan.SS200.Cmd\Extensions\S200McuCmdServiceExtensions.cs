using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Utilities;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Enums.Command;

namespace Zishan.SS200.Cmd.Extensions
{
    /// <summary>
    /// S200McuCmdService扩展方法
    /// </summary>
    public static class S200McuCmdServiceExtensions
    {
        #region 任意设备通用MODBUS命令

        /// <summary>
        /// 执行任意设备的任意MODBUS命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="deviceType">设备类型</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>执行结果</returns>
        public static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteDeviceCommandAsync(
            this IS200McuCmdService cmdService,
            EnuMcuDeviceType deviceType,
            string commandCode,
            List<ushort> parameters = null)
        {
            return await ModbusCommandUtility.ExecuteDeviceCommand(cmdService, deviceType, commandCode, parameters);
        }

        #endregion 任意设备通用MODBUS命令

        #region 机器人命令

        /// <summary>
        /// 执行机器人命令，使用EnuRobotCmd枚举【执行CommandSpec中的命令，暂时不用】
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="robotCommand">Robot命令枚举</param>
        /// <param name="parameters">可选参数列表</param>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <returns>执行结果</returns>
        public static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteRobotCommandAsync(
            this IS200McuCmdService cmdService,
            EnuRobotCmd robotCommand,
            List<ushort> parameters = null,
            int? timeout = null)
        {
            // 将枚举值转换为字符串命令代码
            string commandCode = robotCommand.ToString();

            // 调用Commands.CommandSpec.CommandServiceExtensions中的ExecuteRobotCommandAsync方法
            return await Commands.CommandSpec.CommandServiceExtensions.ExecuteRobotCommandAsync(
                cmdService,
                commandCode,
                parameters,
                timeout);
        }

        /// <summary>
        /// 执行机器人命令，带步进值参数
        /// </summary>
        /// <remarks>
        /// 将commandCode参数从string类型改为EnuRobotCmdIndex枚举类型，
        /// 提高了类型安全性，但可能影响外部调用此方法的代码。
        /// 如果有其他地方直接使用字符串调用此方法，需要相应修改为使用枚举。
        /// </remarks>
        /// <param name="cmdService">命令服务</param>
        /// <param name="commandCode">机器人命令枚举(EnuRobotCmdIndex)，如Move_R_Axis, Move_T_Axis, Move_Z_Axis等</param>
        /// <param name="steps">步进值</param>
        /// <param name="startSloope">起始斜率</param>
        /// <param name="stopSloope">终止斜率</param>
        /// <param name="runCurrent">运行电流</param>
        /// <returns>执行结果</returns>
        public static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteRobotCommandAsync(
            this IS200McuCmdService cmdService,
            EnuRobotCmdIndex commandCode,
            int steps = 0,
            ushort startSloope = 8000,
            ushort stopSloope = 8000,
            ushort runCurrent = 500)
        {
            var parameters = ModbusCommandUtility.CreateStepParameters(steps, startSloope, stopSloope, runCurrent);
            return await ExecuteDeviceCommandAsync(cmdService, EnuMcuDeviceType.Robot, commandCode.ToString(), parameters);
        }

        /// <summary>
        /// 执行机器人R轴(伸缩轴)移动命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="steps">步进值，正值表示伸出，负值表示收回</param>
        /// <param name="startSloope">起始斜率，默认值6000适合R轴</param>
        /// <param name="stopSloope">终止斜率，默认值6000适合R轴</param>
        /// <param name="runCurrent">运行电流，默认值600适合R轴</param>
        /// <returns>执行结果、运行信息和返回信息</returns>
        public static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteRobotRAxisCommandAsync(
            this IS200McuCmdService cmdService,
            int steps = 0,
            ushort startSloope = 6000,
            ushort stopSloope = 6000,
            ushort runCurrent = 600)
        {
            return await ExecuteRobotCommandAsync(cmdService, EnuRobotCmdIndex.Move_R_Axis, steps, startSloope, stopSloope, runCurrent);
        }

        /// <summary>
        /// 执行机器人T轴(旋转轴)移动命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="steps">步进值，正值表示顺时针旋转，负值表示逆时针旋转</param>
        /// <param name="startSloope">起始斜率，默认值25000适合T轴</param>
        /// <param name="stopSloope">终止斜率，默认值25000适合T轴</param>
        /// <param name="runCurrent">运行电流，默认值1000适合T轴</param>
        /// <returns>执行结果、运行信息和返回信息</returns>
        public static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteRobotTAxisCommandAsync(
            this IS200McuCmdService cmdService,
            int steps = 0,
            ushort startSloope = 25000,
            ushort stopSloope = 25000,
            ushort runCurrent = 1000)
        {
            return await ExecuteRobotCommandAsync(cmdService, EnuRobotCmdIndex.Move_T_Axis, steps, startSloope, stopSloope, runCurrent);
        }

        /// <summary>
        /// 执行机器人Z轴(升降轴)移动命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="steps">步进值，正值表示上升，负值表示下降</param>
        /// <param name="startSloope">起始斜率，默认值8000适合Z轴</param>
        /// <param name="stopSloope">终止斜率，默认值8000适合Z轴</param>
        /// <param name="runCurrent">运行电流，默认值500适合Z轴</param>
        /// <returns>执行结果、运行信息和返回信息</returns>
        public static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteRobotZAxisCommandAsync(
            this IS200McuCmdService cmdService,
            int steps = 0,
            ushort startSloope = 8000,
            ushort stopSloope = 8000,
            ushort runCurrent = 500)
        {
            return await ExecuteRobotCommandAsync(cmdService, EnuRobotCmdIndex.Move_Z_Axis, steps, startSloope, stopSloope, runCurrent);
        }

        #endregion 机器人命令

        #region Shuttle命令

        /// <summary>
        /// 执行Shuttle命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>执行结果</returns>
        public static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteShuttleCommandAsync(
            this IS200McuCmdService cmdService,
            string commandCode,
            List<ushort> parameters = null)
        {
            return await ExecuteDeviceCommandAsync(cmdService, EnuMcuDeviceType.Shuttle, commandCode, parameters);
        }

        #endregion Shuttle命令

        #region ChamberA 命令

        /// <summary>
        /// 执行腔室A命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>执行结果</returns>
        public static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteChaCommandAsync(
            this IS200McuCmdService cmdService,
            string commandCode,
            List<ushort> parameters = null)
        {
            return await ExecuteDeviceCommandAsync(cmdService, EnuMcuDeviceType.ChamberA, commandCode, parameters);
        }

        #endregion ChamberA 命令

        #region ChamberB 命令

        /// <summary>
        /// 执行腔室B命令
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="commandCode">命令代码</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>执行结果</returns>
        public static async Task<(string Response, ushort RunInfo, ushort ReturnInfo)> ExecuteChbCommandAsync(
            this IS200McuCmdService cmdService,
            string commandCode,
            List<ushort> parameters = null)
        {
            return await ExecuteDeviceCommandAsync(cmdService, EnuMcuDeviceType.ChamberB, commandCode, parameters);
        }

        #endregion ChamberB 命令
    }
}