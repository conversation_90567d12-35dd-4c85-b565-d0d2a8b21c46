using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using log4net;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;

namespace Zishan.SS200.Cmd.Services
{
    /// <summary>
    /// S200McuCmdService使用示例
    /// </summary>
    public class S200McuCmdServiceExample
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(S200McuCmdServiceExample));
        private readonly IS200McuCmdService _s200McuCmdService;

        /// <summary>
        /// 默认构造函数，使用单例实例
        /// </summary>
        public S200McuCmdServiceExample()
        {
            _s200McuCmdService = S200McuCmdService.Instance;
        }

        /// <summary>
        /// 带依赖注入的构造函数，用于测试
        /// </summary>
        /// <param name="s200McuCmdService">模拟的S200McuCmdService</param>
        public S200McuCmdServiceExample(IS200McuCmdService s200McuCmdService)
        {
            _s200McuCmdService = s200McuCmdService ?? throw new ArgumentNullException(nameof(s200McuCmdService));
        }

        /// <summary>
        /// 示例1: 连接所有设备
        /// </summary>
        public async Task Example1_ConnectAllDevicesAsync()
        {
            _logger.Info("开始执行示例1: 连接所有设备");

            try
            {
                // 连接所有设备
                await _s200McuCmdService.ConnectAllAsync(
                    "127.0.0.1", 502,
                    "127.0.0.1", 503,
                    "127.0.0.1", 504,
                    "127.0.0.1", 505);

                // 记录连接状态
                _logger.Info($"Shuttle连接状态: {_s200McuCmdService.Shuttle.IsConnected}");
                _logger.Info($"Robot连接状态: {_s200McuCmdService.Robot.IsConnected}");
                _logger.Info($"Cha连接状态: {_s200McuCmdService.ChamberA.IsConnected}");
                _logger.Info($"Chb连接状态: {_s200McuCmdService.ChamberB.IsConnected}");
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例1失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 示例2: 执行Shuttle命令
        /// </summary>
        public async Task<string> Example2_RunShuttleCommandAsync()
        {
            _logger.Info("开始执行示例2: 执行Shuttle命令");

            try
            {
                // 确保Shuttle已连接
                if (!_s200McuCmdService.Shuttle.IsConnected)
                {
                    string errorMsg = "Shuttle设备未连接，无法执行命令";
                    _logger.Error(errorMsg);
                    return $"Error: {errorMsg}";
                }

                // 执行命令
                var result = await _s200McuCmdService.Shuttle.Run(EnuShuttleCmdIndex.S1_SD, null);
                string response = result.Response;
                ushort runInfo = result.RunInfo;
                _logger.Info($"执行结果: {response}, 运行信息: {runInfo}");

                return response;
            }
            catch (Exception ex)
            {
                string errorMsg = $"执行示例2失败: {ex.Message}";
                _logger.Error(errorMsg, ex);
                return $"Error: {errorMsg}";
            }
        }

        /// <summary>
        /// 示例3: 执行Robot命令
        /// </summary>
        public async Task<string> Example3_RunRobotCommandAsync()
        {
            _logger.Info("开始执行示例3: 执行Robot命令");

            try
            {
                // 确保Robot已连接
                if (!_s200McuCmdService.Robot.IsConnected)
                {
                    string errorMsg = "Robot设备未连接，无法执行命令";
                    _logger.Error(errorMsg);
                    return $"Error: {errorMsg}";
                }

                // 执行命令
                var robotParameters = new List<ushort> { 1, 2, 3 };
                // 使用EnuRobotCmdIndex.MoveMotor作为示例，实际应根据需要选择正确的命令
                var result = await _s200McuCmdService.Robot.Run(EnuRobotCmdIndex.MoveMotor, robotParameters);
                string response = result.Response;
                ushort runInfo = result.RunInfo;
                _logger.Info($"执行结果: {response}, 运行信息: {runInfo}");

                return response;
            }
            catch (Exception ex)
            {
                string errorMsg = $"执行示例3失败: {ex.Message}";
                _logger.Error(errorMsg, ex);
                return $"Error: {errorMsg}";
            }
        }

        /// <summary>
        /// 示例4: 执行Cha命令
        /// </summary>
        public async Task<string> Example4_RunChaCommandAsync()
        {
            _logger.Info("开始执行示例4: 执行Cha命令");

            try
            {
                // 确保Cha已连接
                if (!_s200McuCmdService.ChamberA.IsConnected)
                {
                    string errorMsg = "Cha设备未连接，无法执行命令";
                    _logger.Error(errorMsg);
                    return $"Error: {errorMsg}";
                }

                // 执行命令
                // 使用EnuCmdIndexInfo.S1_SD作为示例，实际应根据需要选择正确的命令
                var result = await _s200McuCmdService.ChamberA.Run(EnuShuttleCmdIndex.S1_SD, null);
                string response = result.Response;
                ushort runInfo = result.RunInfo;
                _logger.Info($"执行结果: {response}, 运行信息: {runInfo}");

                return response;
            }
            catch (Exception ex)
            {
                string errorMsg = $"执行示例4失败: {ex.Message}";
                _logger.Error(errorMsg, ex);
                return $"Error: {errorMsg}";
            }
        }

        /// <summary>
        /// 示例5: 重置设备
        /// </summary>
        public async Task<bool> Example5_ResetDeviceAsync()
        {
            _logger.Info("开始执行示例5: 重置设备");

            try
            {
                // 首先确保设备已连接
                if (!_s200McuCmdService.Shuttle.IsConnected)
                {
                    _logger.Error("Shuttle设备未连接，无法重置");
                    return false;
                }

                // 重置设备
                var result = await _s200McuCmdService.Shuttle.ResetTaskAsync();
                _logger.Info($"重置结果: {result}");

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例5失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 示例6: 获取设备诊断信息
        /// </summary>
        public void Example6_GetDeviceDiagnostics()
        {
            _logger.Info("开始执行示例6: 获取设备诊断信息");

            try
            {
                // 获取Shuttle设备状态
                _logger.Info($"Shuttle设备状态: {_s200McuCmdService.Shuttle.Status}");
                _logger.Info($"Shuttle设备连接状态: {_s200McuCmdService.Shuttle.IsConnected}");

                // 获取Robot设备状态
                _logger.Info($"Robot设备状态: {_s200McuCmdService.Robot.Status}");
                _logger.Info($"Robot设备连接状态: {_s200McuCmdService.Robot.IsConnected}");

                // 获取Cha设备状态
                _logger.Info($"Cha设备状态: {_s200McuCmdService.ChamberA.Status}");
                _logger.Info($"Cha设备连接状态: {_s200McuCmdService.ChamberA.IsConnected}");

                // 获取Chb设备状态
                _logger.Info($"Chb设备状态: {_s200McuCmdService.ChamberB.Status}");
                _logger.Info($"Chb设备连接状态: {_s200McuCmdService.ChamberB.IsConnected}");

                _logger.Info("设备诊断信息获取完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例6失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 示例7: 断开所有设备连接
        /// </summary>
        public async Task Example7_DisconnectAllAsync()
        {
            _logger.Info("开始执行示例7: 断开所有设备连接");

            try
            {
                // 断开所有设备连接
                await _s200McuCmdService.DisconnectAllAsync();

                // 记录断开连接状态
                _logger.Info($"Shuttle连接状态: {_s200McuCmdService.Shuttle.IsConnected}");
                _logger.Info($"Robot连接状态: {_s200McuCmdService.Robot.IsConnected}");
                _logger.Info($"Cha连接状态: {_s200McuCmdService.ChamberA.IsConnected}");
                _logger.Info($"Chb连接状态: {_s200McuCmdService.ChamberB.IsConnected}");
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例7失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 执行完整示例，展示基本功能
        /// </summary>
        public async Task RunFullExampleAsync()
        {
            _logger.Info("开始执行完整示例流程");

            try
            {
                // 连接所有设备
                await Example1_ConnectAllDevicesAsync();

                // 依次执行各设备命令
                await Example2_RunShuttleCommandAsync();
                await Example3_RunRobotCommandAsync();
                await Example4_RunChaCommandAsync();

                // 获取设备诊断信息
                Example6_GetDeviceDiagnostics();

                // 重置设备
                await Example5_ResetDeviceAsync();

                // 断开所有设备
                await Example7_DisconnectAllAsync();

                _logger.Info("完整示例流程执行完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"执行完整示例流程失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 示例8: 使用新的命令处理器执行Shuttle命令
        /// </summary>
        public async Task<Services.TaskHandleResult> Example8_UseCommandHandlerWithShuttleAsync()
        {
            _logger.Info("开始执行示例8: 使用新的命令处理器执行Shuttle命令");

            try
            {
                // 确保Shuttle已连接
                if (!_s200McuCmdService.Shuttle.IsConnected)
                {
                    _logger.Error("Shuttle设备未连接，无法执行命令");
                    return Services.TaskHandleResult.Failed;
                }

                // 使用新的命令处理系统执行命令
                var result = await _s200McuCmdService.ExecuteDeviceCommandAsync(EnuMcuDeviceType.Shuttle, EnuShuttleCmdIndex.S1_SD);
                _logger.Info($"执行命令结果: {result}");

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例8失败: {ex.Message}", ex);
                return Services.TaskHandleResult.Failed;
            }
        }

        /// <summary>
        /// 示例9: 使用新的命令处理器执行Robot命令
        /// </summary>
        public async Task<Services.TaskHandleResult> Example9_UseCommandHandlerWithRobotAsync()
        {
            _logger.Info("开始执行示例9: 使用新的命令处理器执行Robot命令");

            try
            {
                // 确保Robot已连接
                if (!_s200McuCmdService.Robot.IsConnected)
                {
                    _logger.Error("Robot设备未连接，无法执行命令");
                    return Services.TaskHandleResult.Failed;
                }

                // 使用新的命令处理系统执行命令
                var result = await _s200McuCmdService.ExecuteDeviceCommandAsync(EnuMcuDeviceType.Robot, EnuRobotCmdIndex.MoveMotor);
                _logger.Info($"执行命令结果: {result}");

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例9失败: {ex.Message}", ex);
                return Services.TaskHandleResult.Failed;
            }
        }

        /// <summary>
        /// 示例10: 使用新的命令处理器执行Cha命令
        /// </summary>
        public async Task<Services.TaskHandleResult> Example10_UseCommandHandlerWithChaAsync()
        {
            _logger.Info("开始执行示例10: 使用新的命令处理器执行Cha命令");

            try
            {
                // 确保Cha已连接
                if (!_s200McuCmdService.ChamberA.IsConnected)
                {
                    _logger.Error("Cha设备未连接，无法执行命令");
                    return Services.TaskHandleResult.Failed;
                }

                // 使用新的命令处理系统执行命令
                var result = await _s200McuCmdService.ExecuteDeviceCommandAsync(EnuMcuDeviceType.ChamberA, EnuChaCmdIndex.OD_SD);
                _logger.Info($"执行命令结果: {result}");

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例10失败: {ex.Message}", ex);
                return Services.TaskHandleResult.Failed;
            }
        }

        /// <summary>
        /// 示例11: 使用新的命令处理器执行带参数的命令
        /// </summary>
        public async Task<Services.TaskHandleResult> Example11_UseCommandHandlerWithParametersAsync()
        {
            _logger.Info("开始执行示例11: 使用新的命令处理器执行带参数的命令");

            try
            {
                // 确保Shuttle已连接
                if (!_s200McuCmdService.Shuttle.IsConnected)
                {
                    _logger.Error("Shuttle设备未连接，无法执行命令");
                    return Services.TaskHandleResult.Failed;
                }

                // 定义参数
                ushort[] shuttleParameters = new ushort[] { 1, 2, 3 };

                // 使用新的命令处理系统执行带参数的命令
                var result = await _s200McuCmdService.ExecuteDeviceCommandAsync(EnuMcuDeviceType.Shuttle, EnuShuttleCmdIndex.S11_OV_SV, shuttleParameters);
                _logger.Info($"执行命令结果: {result}");

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"执行示例11失败: {ex.Message}", ex);
                return Services.TaskHandleResult.Failed;
            }
        }

        /// <summary>
        /// 执行完整示例，展示新的命令处理系统
        /// </summary>
        public async Task RunCommandHandlerExampleAsync()
        {
            _logger.Info("开始执行新的命令处理系统示例");

            try
            {
                // 连接所有设备
                await _s200McuCmdService.ConnectAllAsync(
                    "192.168.1.101", 502,
                    "192.168.1.102", 502,
                    "192.168.1.103", 502,
                    "192.168.1.104", 502);

                // 依次执行各设备命令
                await Example8_UseCommandHandlerWithShuttleAsync();
                await Example9_UseCommandHandlerWithRobotAsync();
                await Example10_UseCommandHandlerWithChaAsync();
                await Example11_UseCommandHandlerWithParametersAsync();

                // 断开所有设备
                await _s200McuCmdService.DisconnectAllAsync();

                _logger.Info("新的命令处理系统示例执行完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"执行新的命令处理系统示例失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 执行机器人命令示例
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<string> ExecuteRobotCommandExampleAsync()
        {
            // 方式1：使用设备扩展方法
            var result1 = await _s200McuCmdService.ExecuteRobotTAxisCommandAsync(
                10000,              // 步进值
                8000,               // 起始斜率
                8000,               // 终止斜率
                500                 // 运行电流
            );

            // 方式2：使用通用扩展方法
            var robotParameters = new List<ushort> { 0, 10000, 8000, 8000, 500 };
            var result2 = await _s200McuCmdService.ExecuteDeviceCommandAsync(
                EnuMcuDeviceType.Robot,            // 设备名称
                EnuRobotCmdIndex.Move_T_Axis,      // 使用枚举类型
                robotParameters.ToArray()          // 参数列表
            );

            return $"方式1结果: {result1.Response}\n方式2结果: {result2}";
        }

        /// <summary>
        /// 执行Shuttle命令示例
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<string> ExecuteShuttleCommandExampleAsync()
        {
            // 执行Shuttle下降命令 (S1 SD)
            var result = await _s200McuCmdService.ExecuteShuttleCommandAsync("S1 SD");
            return $"Shuttle下降命令结果: {result.Response}";
        }

        /// <summary>
        /// 执行腔室A命令示例
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<string> ExecuteChaCommandExampleAsync()
        {
            // 执行腔室A打开命令 (OD_SD)
            var result = await _s200McuCmdService.ExecuteChaCommandAsync("OD_SD");
            return $"腔室A打开命令结果: {result.Response}";
        }

        /// <summary>
        /// 执行腔室B命令示例
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<string> ExecuteChbCommandExampleAsync()
        {
            // 执行腔室B打开命令 (OD_SD)
            var result = await _s200McuCmdService.ExecuteChbCommandAsync("OD_SD");
            return $"腔室B打开命令结果: {result.Response}";
        }

        /// <summary>
        /// 批量执行多个命令的示例
        /// </summary>
        /// <returns>执行结果</returns>
        public async Task<List<string>> ExecuteBatchCommandsExampleAsync()
        {
            var results = new List<string>();

            // 机器人命令
            var robotResult = await _s200McuCmdService.ExecuteRobotTAxisCommandAsync(10000);
            results.Add($"机器人命令结果: {robotResult.Response}");

            // Shuttle命令
            var shuttleResult = await _s200McuCmdService.ExecuteShuttleCommandAsync("S1_SD");
            results.Add($"Shuttle命令结果: {shuttleResult.Response}");

            // 腔室A命令
            var chaResult = await _s200McuCmdService.ExecuteChaCommandAsync("OD_SD");
            results.Add($"腔室A命令结果: {chaResult.Response}");

            // 腔室B命令
            var chbResult = await _s200McuCmdService.ExecuteChbCommandAsync("OD_SD");
            results.Add($"腔室B命令结果: {chbResult.Response}");

            return results;
        }
    }
}